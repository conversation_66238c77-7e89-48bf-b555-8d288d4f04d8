#!/bin/bash

# Script to backup and restore original configuration files
# Usage: ./scripts/config_manager.sh <action>
# Actions: backup, restore

set -e

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# Get the project root directory (parent of scripts directory)
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Default backup directory (can be overridden by environment variable)
BACKUP_DIR="${CONFIG_BACKUP_DIR:-${SCRIPT_DIR}/backups}"

# Ensure backup directory is absolute
if [[ ! "$BACKUP_DIR" = /* ]]; then
    BACKUP_DIR="$PROJECT_ROOT/$BACKUP_DIR"
fi
CONFIG_FILES=(
    "$PROJECT_ROOT/lib/constant/base.dart"
    "$PROJECT_ROOT/android/app/src/main/kotlin/com/kabu/kabuappNext/Application.kt"
    "$PROJECT_ROOT/ios/Runner/AppDelegate.swift"
    "$PROJECT_ROOT/ios/config/dev/GoogleService-Info.plist"
    "$PROJECT_ROOT/ios/config/prod/GoogleService-Info.plist"
    "$PROJECT_ROOT/ios/config/stg/GoogleService-Info.plist"
    "$PROJECT_ROOT/ios/config/adt/GoogleService-Info.plist"
    "$PROJECT_ROOT/ios/config/st/GoogleService-Info.plist"
    "$PROJECT_ROOT/android/app/src/develop/google-services.json"
    "$PROJECT_ROOT/android/app/src/production/google-services.json"
    "$PROJECT_ROOT/android/app/src/staging/google-services.json"
    "$PROJECT_ROOT/android/app/src/adt/google-services.json"
    "$PROJECT_ROOT/android/app/src/st/google-services.json"
)

backup_files() {
    echo "Creating backup of configuration files..."
    echo "Project root: $PROJECT_ROOT"
    echo "Backup directory: $BACKUP_DIR"
    mkdir -p "$BACKUP_DIR"
    
    for file in "${CONFIG_FILES[@]}"; do
        if [ -f "$file" ]; then
            # Create relative path for backup filename
            relative_path="${file#$PROJECT_ROOT/}"
            backup_path="$BACKUP_DIR/$(echo "$relative_path" | sed 's|/|_|g')"
            cp "$file" "$backup_path"
            echo "✓ Backed up: $relative_path -> $(basename "$backup_path")"
        else
            relative_path="${file#$PROJECT_ROOT/}"
            echo "⚠ Warning: File $relative_path not found"
        fi
    done
    
    echo "✅ Backup completed to $BACKUP_DIR"
}

restore_files() {
    echo "Restoring configuration files from backup..."
    echo "Project root: $PROJECT_ROOT"
    echo "Backup directory: $BACKUP_DIR"
    
    if [ ! -d "$BACKUP_DIR" ]; then
        echo "❌ Error: Backup directory $BACKUP_DIR not found"
        echo "Please run backup first: $0 backup"
        exit 1
    fi
    
    for file in "${CONFIG_FILES[@]}"; do
        relative_path="${file#$PROJECT_ROOT/}"
        backup_path="$BACKUP_DIR/$(echo "$relative_path" | sed 's|/|_|g')"
        if [ -f "$backup_path" ]; then
            # Create directory if it doesn't exist
            mkdir -p "$(dirname "$file")"
            cp "$backup_path" "$file"
            echo "✓ Restored: $(basename "$backup_path") -> $relative_path"
        else
            echo "⚠ Warning: Backup file $(basename "$backup_path") not found"
        fi
    done
    
    echo "✅ Restore completed from $BACKUP_DIR"
}

show_help() {
    echo "Configuration Files Manager"
    echo ""
    echo "Usage: $0 <action>"
    echo ""
    echo "Actions:"
    echo "  backup   - Create backup of all configuration files"
    echo "  restore  - Restore configuration files from backup"
    echo "  help     - Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  CONFIG_BACKUP_DIR - Custom backup directory (default: scripts/backups)"
    echo ""
    echo "Examples:"
    echo "  $0 backup"
    echo "  $0 restore"
    echo "  CONFIG_BACKUP_DIR=/tmp/config-backup $0 backup"
    echo ""
    echo "Current settings:"
    echo "  Project root: $PROJECT_ROOT"
    echo "  Backup directory: $BACKUP_DIR"
}

case "${1:-}" in
    backup)
        backup_files
        ;;
    restore)
        restore_files
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "❌ Error: Invalid action '${1:-}'"
        echo ""
        show_help
        exit 1
        ;;
esac
