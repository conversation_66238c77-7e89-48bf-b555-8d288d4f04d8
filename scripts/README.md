# Environment Configuration Script

Bash script to apply environment variables from `envs` directory to corresponding configuration files.

## Structure

```
scripts/
├── apply_env.sh          # Bash script to apply env variables
├── config_manager.sh     # Script for backup/restore config files
└── README.md            # This guide file
```

## Environment Variables

The environment variables that are applied:

- `GEO_IP2_TOKEN` → `lib/constant/base.dart`
- `KARTE_KEY` → `android/app/src/main/kotlin/com/kabu/kabuappNext/Application.kt`
- `KARTE_KEY` → `ios/Runner/AppDelegate.swift`
- `FIREBASE_API_KEY_IOS` → `ios/config/*/GoogleService-Info.plist`
- `FIREBASE_API_KEY_ANDROID` → `android/app/src/*/google-services.json`

## Usage

### 1. Using Makefile (Recommended)

```bash
# Apply specific environment
make apply-env ENV=dev      # Apply dev environment
make apply-env ENV=prod     # Apply production environment
make apply-env ENV=stg      # Apply staging environment
make apply-env ENV=adt      # Apply adt environment
make apply-env ENV=st       # Apply st environment
```

#### Backup and restore config files:

```bash
make backup-config          # Backup all config files
make restore-config         # Restore from backup
```

### 2. Using script directly

```bash
./scripts/apply_env.sh dev
```

### 3. Azure Pipelines

The script is optimized to run on Azure Pipelines. See `azure-pipelines.yml` file for integration example.

```yaml
- task: Bash@3
  displayName: "Apply Environment Variables"
  inputs:
    targetType: "inline"
    script: |
      ./scripts/apply_env.sh $(environment)
```

## Features

### Script Features

- ✅ Cross-platform (macOS, Linux, Windows WSL)
- ✅ Azure Pipelines compatible
- ✅ Uses only `sed` - no dependencies required
- ✅ Error handling and validation
- ✅ Automatic escape special characters

### Environment Loading

- ✅ Load from `.env` files
- ✅ Support comments and empty lines
- ✅ Auto-remove quotes from values
- ✅ Safe variable handling

## Recommended Workflow

1. **Backup config files before making changes:**

   ```bash
   make backup-config
   ```

2. **Apply environment variables:**

   ```bash
   make apply-env ENV=dev
   ```

3. **Build and test the application**

4. **Restore to original state when needed:**
   ```bash
   make restore-config
   ```

## Requirements

### Local Development

- bash
- sed

### Azure Pipelines

No additional dependencies need to be installed, the script only uses tools available in most systems by default.
