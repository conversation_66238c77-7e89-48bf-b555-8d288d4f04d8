#!/bin/bash

# Script to apply environment variables to various files
# Usage: ./scripts/apply_env.sh <environment>
# Example: ./scripts/apply_env.sh dev

set -e

# Check if environment parameter is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 <environment>"
    echo "Available environments: dev, prod, stg, adt, st"
    exit 1
fi

ENV=$1
ENV_FILE="envs/${ENV}.env"

# Check if environment file exists
if [ ! -f "$ENV_FILE" ]; then
    echo "Error: Environment file $ENV_FILE not found"
    exit 1
fi

echo "Applying environment variables from $ENV_FILE..."

# Load environment variables
load_env_vars() {
    while IFS='=' read -r key value || [ -n "$key" ]; do
        # Skip empty lines and comments
        [[ -z "$key" || "$key" == \#* ]] && continue
        
        # Remove quotes from value
        value=$(echo "$value" | sed 's/^["'\'']//' | sed 's/["'\'']$//')
        
        # Export variable
        export "$key"="$value"
    done < "$ENV_FILE"
}

# Function to replace placeholder in file (cross-platform)
replace_in_file() {
    local file="$1"
    local placeholder="$2"
    local value="$3"
    
    if [ -f "$file" ]; then
        # Escape special characters for sed replacement
        local escaped_value
        escaped_value=$(printf '%s\n' "$value" | sed 's/[&/]/\\&/g')
        
        if [[ "$OSTYPE" == "darwin"* ]] || [[ "$OSTYPE" == "freebsd"* ]]; then
            # macOS/BSD
            sed -i '' "s|$placeholder|$escaped_value|g" "$file"
        else
            # Linux (Azure Pipelines)
            sed -i "s|$placeholder|$escaped_value|g" "$file"
        fi
        echo "✓ Updated $file: $placeholder"
    else
        echo "⚠ Warning: File $file not found"
    fi
}

# Function to update JSON file using sed
update_json_file() {
    local file="$1"
    local placeholder="$2"
    local value="$3"
    
    if [ ! -f "$file" ]; then
        echo "⚠ Warning: File $file not found"
        return
    fi
    
    # Use sed to replace in JSON files
    replace_in_file "$file" "$placeholder" "$value"
}

load_env_vars

# Map environment to corresponding directories
case "$ENV" in
    "dev")
        IOS_CONFIG_DIR="ios/config/dev"
        ANDROID_CONFIG_DIR="android/app/src/develop"
        ;;
    "prod")
        IOS_CONFIG_DIR="ios/config/prod"
        ANDROID_CONFIG_DIR="android/app/src/production"
        ;;
    "stg")
        IOS_CONFIG_DIR="ios/config/stg"
        ANDROID_CONFIG_DIR="android/app/src/staging"
        ;;
    "adt")
        IOS_CONFIG_DIR="ios/config/adt"
        ANDROID_CONFIG_DIR="android/app/src/adt"
        ;;
    "st")
        IOS_CONFIG_DIR="ios/config/st"
        ANDROID_CONFIG_DIR="android/app/src/st"
        ;;
    *)
        echo "Error: Unknown environment '$ENV'"
        echo "Available environments: dev, prod, stg, adt, st"
        exit 1
        ;;
esac

# 1. Apply GEO_IP2_TOKEN to lib/constant/base.dart
echo "1. Updating GEO_IP2_TOKEN in lib/constant/base.dart..."
if [ -n "$GEO_IP2_TOKEN" ]; then
    replace_in_file "lib/constant/base.dart" "GEO_IP2_TOKEN" "$GEO_IP2_TOKEN"
fi

# 2. Apply KARTE_KEY to Android Application.kt
echo "2. Updating KARTE_KEY in Android Application.kt..."
if [ -n "$KARTE_KEY" ]; then
    replace_in_file "android/app/src/main/kotlin/com/kabu/kabuappNext/Application.kt" "KARTE_KEY" "$KARTE_KEY"
fi

# 3. Apply KARTE_KEY to iOS AppDelegate.swift
echo "3. Updating KARTE_KEY in iOS AppDelegate.swift..."
if [ -n "$KARTE_KEY" ]; then
    replace_in_file "ios/Runner/AppDelegate.swift" "KARTE_KEY" "$KARTE_KEY"
fi

# 4. Apply FIREBASE_API_KEY_IOS to iOS GoogleService-Info.plist files
echo "4. Updating FIREBASE_API_KEY_IOS in iOS config files..."
if [ -n "$FIREBASE_API_KEY_IOS" ]; then
    plist_file="$IOS_CONFIG_DIR/GoogleService-Info.plist"
    if [ -f "$plist_file" ]; then
        replace_in_file "$plist_file" "FIREBASE_API_KEY_IOS" "$FIREBASE_API_KEY_IOS"
    else
        echo "⚠ Warning: iOS config file $plist_file not found for environment $ENV"
    fi
fi

# 5. Apply FIREBASE_API_KEY_ANDROID to Android google-services.json files
echo "5. Updating FIREBASE_API_KEY_ANDROID in Android config files..."
if [ -n "$FIREBASE_API_KEY_ANDROID" ]; then
    json_file="$ANDROID_CONFIG_DIR/google-services.json"
    if [ -f "$json_file" ]; then
        update_json_file "$json_file" "FIREBASE_API_KEY_ANDROID" "$FIREBASE_API_KEY_ANDROID"
    else
        echo "⚠ Warning: Android config file $json_file not found for environment $ENV"
    fi
fi

echo ""
echo "✅ Environment variables from $ENV_FILE have been applied successfully!"
