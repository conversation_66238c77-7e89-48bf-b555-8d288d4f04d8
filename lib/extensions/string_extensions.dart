import 'package:flutter/material.dart';
import 'package:kc_member_site_native/constant/app_config.dart';
import 'package:kc_member_site_native/constant/base.dart';

extension StringExtensions on String? {
  String decodeFull() {
    if (this == null) return '';
    String value = this!;
    try {
      value = Uri.decodeFull(value);
    } catch (e) {
      debugPrint(e.toString());
    }
    return value;
  }

  bool isPdfUrl() {
    if (this == null) return false;
    final uri = Uri.tryParse(this!);
    final path = uri?.path ?? (this?.split("?")[0] ?? "");
    return path.toLowerCase().endsWith(".pdf");
  }

  String useCorrectEllipsis() {
    return this?.replaceAll('', '\u200B') ?? "";
  }

  bool get isNullOrEmpty => this?.isEmpty ?? true;

  bool get isNotNullOrEmpty => this?.isNotEmpty ?? false;

  bool shouldExternalBrowser() {
    final url = this;
    final uri = Uri.tryParse(url ?? "");
    String domain = appConfigModel.app_config.wl.domain.firstWhere((element) => element == uri?.host, orElse: () => '');
    final urlDecode = url.decodeFull().toLowerCase();
    String path = appConfigModel.app_config.openwithbrowser.urlincludes
        .firstWhere((element) => urlDecode.contains(element.decodeFull().toLowerCase()), orElse: () => '');
    return domain.isNotEmpty && path.isNotEmpty || domain.isEmpty;
  }

  DateTime? toDateTime() {
    return isNullOrEmpty ? null : DateTime.tryParse(this!.replaceAll('/', '-'));
  }

  String? checkAuthSecuritySettingUrl() {
    final url = this ?? "";
    if (url.startsWith(Base.authSecurityLoginUrl)) return Base.authSecurityLoginUrl;
    if (url.startsWith(Base.authSecurityMfaLoginUrl)) return Base.authSecurityMfaLoginUrl;
    return null;
  }
}
