import 'dart:io';

import 'package:chopper/chopper.dart';
import 'package:http/io_client.dart';
import 'package:kc_member_site_native/constant/base.dart';
import 'package:kc_member_site_native/repository/client/interceptor.dart';

class KcmApiClientCreator {
  ChopperClient create({String? baseUrl, int? timeout}) {
    final httpClient = HttpClient();
    if (timeout != null) {
      httpClient.connectionTimeout = Duration(seconds: timeout);
    }
    return ChopperClient(
      baseUrl: Uri.parse(baseUrl ?? Base.bffDomainUrl),
      converter: const JsonConverter(),
      errorConverter: const JsonConverter(),
      interceptors: [
        const KcmRequestInterceptor(),
        const ResponseInterceptor(),
        CurlInterceptor(),
      ],
      client: IOClient(httpClient),
    );
  }
}
