import 'dart:async';

import 'package:chopper/chopper.dart';
import 'package:flutter_neumorphic/flutter_neumorphic.dart';

class BffRequestInterceptor implements Interceptor {
  const BffRequestInterceptor();

  @override
  FutureOr<Response<BodyType>> intercept<BodyType>(Chain<BodyType> chain) async {
    final request = applyHeaders(chain.request, {
      contentTypeKey: formEncodedHeaders,
      "Accept": "*/*",
      "Accept-Encoding": "gzip",
    });

    return chain.proceed(request);
  }
}

class KcmRequestInterceptor implements Interceptor {
  const KcmRequestInterceptor();

  @override
  FutureOr<Response<BodyType>> intercept<BodyType>(Chain<BodyType> chain) async {
    final request = applyHeaders(chain.request, {
      contentTypeKey: jsonHeaders,
      "Accept": "*/*",
      "Accept-Encoding": "gzip",
    });

    return chain.proceed(request);
  }
}

class ResponseInterceptor implements Interceptor {
  const ResponseInterceptor();

  @override
  FutureOr<Response<BodyType>> intercept<BodyType>(Chain<BodyType> chain) async {
    final response = await chain.proceed(chain.request);
    debugPrint("""
                === Response log
                ${response.statusCode} ${response.base.request?.method} ${response.base.request?.url}
                headers: ${response.headers}
                body: ${response.body}
                ===
              """);
    return response;
  }
}
