import 'dart:io';

import 'package:chopper/chopper.dart';
import 'package:http/io_client.dart';
import 'package:kc_member_site_native/constant/base.dart';
import 'package:kc_member_site_native/repository/client/interceptor.dart';

class BffClientCreator {
  ChopperClient create() {
    return ChopperClient(
      baseUrl: Uri.parse(Base.bffDomainUrl),
      converter: const FormUrlEncodedConverter(),
      errorConverter: const JsonConverter(),
      interceptors: [
        const BffRequestInterceptor(),
        const ResponseInterceptor(),
        CurlInterceptor(),
      ],
      client: IOClient(HttpClient()),
    );
  }
}
