// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_repository.dart';

// **************************************************************************
// ChopperGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
final class _$LoginRepository extends LoginRepository {
  _$LoginRepository([ChopperClient? client]) {
    if (client == null) return;
    this.client = client;
  }

  @override
  final Type definitionType = LoginRepository;

  @override
  Future<Response<dynamic>> spNativeLoginRop({
    required String username,
    required String password,
    String? appCheckToken,
  }) {
    final Uri $url = Uri.parse('/auth/sp-native/login-rop');
    final Map<String, String> $headers = {
      if (appCheckToken != null) 'X-Firebase-AppCheck': appCheckToken,
    };
    final $body = <String, dynamic>{
      'username': username,
      'password': password,
    };
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> authMfaOauthToken({
    required String? xt,
    required String cookie,
    required String grantType,
    String? oobCode,
    String? otp,
    String? bindingCode,
    String? recoveryCode,
  }) {
    final Uri $url = Uri.parse('/auth/mfa/oauth-token');
    final Map<String, String> $headers = {
      if (xt != null) 'x-xsrf-token': xt,
      'cookie': cookie,
    };
    final $body = <String, dynamic>{
      'grant_type': grantType,
      'oob_code': oobCode,
      'otp': otp,
      'binding_code': bindingCode,
      'recovery_code': recoveryCode,
    };
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }
}
