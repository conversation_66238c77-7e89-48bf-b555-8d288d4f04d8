// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_version_repository.dart';

// **************************************************************************
// ChopperGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
final class _$AppVersionRepository extends AppVersionRepository {
  _$AppVersionRepository([ChopperClient? client]) {
    if (client == null) return;
    this.client = client;
  }

  @override
  final Type definitionType = AppVersionRepository;

  @override
  Future<Response<dynamic>> checkVersion({
    required String platformType,
    required int majorVersion,
    required int minorVersion,
    required int buildVersion,
  }) {
    final Uri $url = Uri.parse(
        '/rpc/ns/appsmanagement/aplicontrol/v1/AppVersionUpConfirmService/CheckVersion');
    final $body = <String, dynamic>{
      'platformType': platformType,
      'majorVersion': majorVersion,
      'minorVersion': minorVersion,
      'buildVersion': buildVersion,
    };
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
    );
    return client.send<dynamic, dynamic>($request);
  }
}
