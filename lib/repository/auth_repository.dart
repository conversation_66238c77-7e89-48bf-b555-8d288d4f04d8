import 'dart:async';

import 'package:chopper/chopper.dart';

part 'auth_repository.chopper.dart';

@ChopperApi(baseUrl: '')
abstract class AuthRepository extends ChopperService {
  static AuthRepository create(ChopperClient client) => _$AuthRepository(client);

  @Post(path: "/auth/native-security-setting/code-registration")
  Future<Response> authCodeRegistration({
    @Field('native_state') required String nativeState,
    @Field('code') required String code,
    @Header('x-xsrf-token') required String? xt,
    @Header('cookie') required String cookie,
  });

  @Get(path: "/auth/mfa/authenticators")
  Future<Response> mfaAuthenticators({
    @Header('x-xsrf-token') required String? xt,
    @Header('cookie') required String cookie,
  });

  @Post(path: "/auth/mfa/challenge")
  Future<Response> authMfaChallenge({
    @Field('challenge_type') required String challengeType,
    @Field('authenticator_id') required String? authenticatorId,
    @Header('x-xsrf-token') required String? xt,
    @Header('cookie') required String cookie,
  });

  @Post(
    path: "/auth/mfa/email-resend",
    optionalBody: true,
  )
  Future<Response> authMfaEmailResend({
    @Header('x-xsrf-token') required String? xt,
    @Header('cookie') required String cookie,
  });
}
