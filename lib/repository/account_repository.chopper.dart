// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_repository.dart';

// **************************************************************************
// ChopperGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
final class _$AccountRepository extends AccountRepository {
  _$AccountRepository([ChopperClient? client]) {
    if (client == null) return;
    this.client = client;
  }

  @override
  final Type definitionType = AccountRepository;

  @override
  Future<Response<dynamic>> getLoginStatus([
    String? xt,
    String? cookie,
  ]) {
    final Uri $url = Uri.parse(
        '/rpc/s/account/account/v1/AccountAttributeService/GetLoginStatus');
    final Map<String, String> $headers = {
      if (xt != null) 'x-xsrf-token': xt,
      if (cookie != null) 'cookie': cookie,
    };
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> registerLoginHistory([
    String? xt,
    String? cookie,
  ]) {
    final Uri $url = Uri.parse(
        '/rpc/s/account/account/v1/AccountAttributeService/RegisterLoginHistory');
    final Map<String, String> $headers = {
      if (xt != null) 'x-xsrf-token': xt,
      if (cookie != null) 'cookie': cookie,
    };
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> getDocumentsReAgreementStatus([
    String? xt,
    String? cookie,
  ]) {
    final Uri $url = Uri.parse(
        '/rpc/s/account/agreement/v2/DocumentsReagreementService/GetDocumentsReAgreementStatus');
    final Map<String, String> $headers = {
      if (xt != null) 'x-xsrf-token': xt,
      if (cookie != null) 'cookie': cookie,
    };
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }
}
