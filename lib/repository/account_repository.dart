import 'package:chopper/chopper.dart';

part 'account_repository.chopper.dart';

@ChopperApi(baseUrl: '/rpc/s/account')
abstract class AccountRepository extends ChopperService {
  static AccountRepository create(ChopperClient client) => _$AccountRepository(client);

  @Post(
    path: "/account/v1/AccountAttributeService/GetLoginStatus",
    optionalBody: true,
  )
  Future<Response> getLoginStatus([
    @Header('x-xsrf-token') String? xt,
    @Header('cookie') String? cookie,
  ]);

  @Post(
    path: "/account/v1/AccountAttributeService/RegisterLoginHistory",
    optionalBody: true,
  )
  Future<Response> registerLoginHistory([
    @Header('x-xsrf-token') String? xt,
    @Header('cookie') String? cookie,
  ]);

  @Post(
    path: "/agreement/v2/DocumentsReagreementService/GetDocumentsReAgreementStatus",
    optionalBody: true,
  )
  Future<Response> getDocumentsReAgreementStatus([
    @Header('x-xsrf-token') String? xt,
    @Header('cookie') String? cookie,
  ]);
}
