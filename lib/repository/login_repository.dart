import 'dart:async';

import 'package:chopper/chopper.dart';

part 'login_repository.chopper.dart';

@ChopperApi(baseUrl: '')
abstract class LoginRepository extends ChopperService {
  static LoginRepository create(ChopperClient client) => _$LoginRepository(client);

  @Post(path: "/auth/sp-native/login-rop")
  Future<Response> spNativeLoginRop({
    @Field() required String username,
    @Field() required String password,
    @Header('X-Firebase-AppCheck') String? appCheckToken,
  });

  @Post(path: "/auth/mfa/oauth-token")
  Future<Response> authMfaOauthToken({
    @Header('x-xsrf-token') required String? xt,
    @Header('cookie') required String cookie,
    @Field('grant_type') required String grantType,
    @Field('oob_code') String? oobCode,
    @Field('otp') String? otp,
    @Field('binding_code') String? bindingCode,
    @Field('recovery_code') String? recoveryCode,
  });
}
