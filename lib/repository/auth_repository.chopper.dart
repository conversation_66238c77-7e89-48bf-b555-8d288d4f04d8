// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_repository.dart';

// **************************************************************************
// ChopperGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
final class _$AuthRepository extends AuthRepository {
  _$AuthRepository([ChopperClient? client]) {
    if (client == null) return;
    this.client = client;
  }

  @override
  final Type definitionType = AuthRepository;

  @override
  Future<Response<dynamic>> authCodeRegistration({
    required String nativeState,
    required String code,
    required String? xt,
    required String cookie,
  }) {
    final Uri $url =
        Uri.parse('/auth/native-security-setting/code-registration');
    final Map<String, String> $headers = {
      if (xt != null) 'x-xsrf-token': xt,
      'cookie': cookie,
    };
    final $body = <String, dynamic>{
      'native_state': nativeState,
      'code': code,
    };
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> mfaAuthenticators({
    required String? xt,
    required String cookie,
  }) {
    final Uri $url = Uri.parse('/auth/mfa/authenticators');
    final Map<String, String> $headers = {
      if (xt != null) 'x-xsrf-token': xt,
      'cookie': cookie,
    };
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> authMfaChallenge({
    required String challengeType,
    required String? authenticatorId,
    required String? xt,
    required String cookie,
  }) {
    final Uri $url = Uri.parse('/auth/mfa/challenge');
    final Map<String, String> $headers = {
      if (xt != null) 'x-xsrf-token': xt,
      'cookie': cookie,
    };
    final $body = <String, dynamic>{
      'challenge_type': challengeType,
      'authenticator_id': authenticatorId,
    };
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> authMfaEmailResend({
    required String? xt,
    required String cookie,
  }) {
    final Uri $url = Uri.parse('/auth/mfa/email-resend');
    final Map<String, String> $headers = {
      if (xt != null) 'x-xsrf-token': xt,
      'cookie': cookie,
    };
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }
}
