import 'package:chopper/chopper.dart';

part 'app_version_repository.chopper.dart';

@ChopperApi(baseUrl: '/rpc/ns/appsmanagement/aplicontrol/v1/AppVersionUpConfirmService/CheckVersion')
abstract class AppVersionRepository extends ChopperService {
  static AppVersionRepository create(ChopperClient client) => _$AppVersionRepository(client);

  @Post(path: "")
  Future<Response> checkVersion(
      {@Field() required String platformType,
      @Field() required int majorVersion,
      @Field() required int minorVersion,
      @Field() required int buildVersion});
}
