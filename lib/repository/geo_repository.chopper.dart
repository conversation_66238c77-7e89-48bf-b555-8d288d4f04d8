// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'geo_repository.dart';

// **************************************************************************
// ChopperGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
final class _$GeoRepository extends GeoRepository {
  _$GeoRepository([ChopperClient? client]) {
    if (client == null) return;
    this.client = client;
  }

  @override
  final Type definitionType = GeoRepository;

  @override
  Future<Response<dynamic>> getGeoLocation({
    String? channel,
    String? geoIP2CheckToken,
  }) {
    final Uri $url = Uri.parse('/geo');
    final Map<String, String> $headers = {
      if (channel != null) 'Channel': channel,
      if (geoIP2CheckToken != null) 'GeoIP2-Check-Token': geoIP2CheckToken,
    };
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }
}
