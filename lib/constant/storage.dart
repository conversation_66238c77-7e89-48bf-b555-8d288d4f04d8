class StorageKey {
  StorageKey._();
  // Secure storage keys
  static const username = "username";
  static const password = "password";
  static const authenticatorId = "authenticator_id";
  static const userIdKarte = "userIdKarte";
  static const appReviewHistory = "appReviewHistory";

  // Shared preferences keys
  static const firstRun = "firstRun";
  static const isAutoLogin = "isAutoLogin";
  static const isSaveUsername = "isSaveUsername";
  static const isSavePassword = "isSavePassword";
  static const buildNumber = "buildNumber";
  static const installDate = "installDate";
}
