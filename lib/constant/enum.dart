import 'package:collection/collection.dart';

enum ActionType {
  unknown(null),
  home("0"),
  registerToken("1"),
  deleteToken("2"),
  updateSettings("3"),
  updateSettingsFail("4"),
  sessionInvalid("5"),
  logout("6");

  final String? value;
  const ActionType(this.value);

  static ActionType from(String? value) {
    return ActionType.values.firstWhere(
      (element) => element.value == value,
      orElse: () => ActionType.unknown,
    );
  }
}

enum AdtType {
  A("A", "d10-spsite-a.devadt.devcabu.jp"),
  ;

  final String value;
  final String domain;
  const AdtType(this.value, this.domain);

  static AdtType from(String? value) {
    return AdtType.values.firstWhere(
      (element) => element.value == value,
      orElse: () => AdtType.A,
    );
  }
}

enum StgType {
  // ITb-OPE
  opeA("OPEA", "d10-spsite-ope-a.dev2.devcabu.jp"),
  opeJ("OPEJ", "d10-spsite-ope-j.dev2.devcabu.jp"),
  // ITb
  nNISA("NNISA", "d10-spsite-nnisa.dev2.devcabu.jp"),
  pUTIB("PUTIB", "d10-spsite-putib.dev2.devcabu.jp"),
  fTDPD("FTDPD", "d10-spsite-ftdpd.dev2.devcabu.jp"),
  iPTRA("IPTRA", "d10-spsite-iptra.dev2.devcabu.jp"),
  nCS1A("NCS1A", "d10-spsite-ncs1a.dev2.devcabu.jp"),
  nCS1B("NCS1B", "d10-spsite-ncs1b.dev2.devcabu.jp"),
  A("A", "d10-spsite-a.dev2.devcabu.jp"),
  G("G", "d10-spsite-g.dev2.devcabu.jp"),
  H("H", "d10-spsite-h.dev2.devcabu.jp"),
  J("J", "d10-spsite-j.dev2.devcabu.jp"),
  F("F", "d10-spsite-f.dev2.devcabu.jp");

  final String value;
  final String domain;
  const StgType(this.value, this.domain);

  static StgType from(String? value) {
    return StgType.values.firstWhere(
      (element) => element.value == value,
      orElse: () => StgType.A,
    );
  }
}

enum StType {
  // ITb-OPE
  opeA("OPEA", "d10-spsite-ope-a.devst.devcabu.jp"),
  opeDua2a("OPE_DUA2A", "d10-spsite-ope-dua2a.devst.devcabu.jp"),
  opeDua2b("OPE_DUA2B", "d10-spsite-ope-dua2b.devst.devcabu.jp"),
  opeSorpe("OPE_SORPE", "d10-spsite-ope-sorpe.devst.devcabu.jp"),
  opePutia("OPE_PUTIA", "d10-spsite-ope-putia.devst.devcabu.jp"),
  opeFrtma("OPE_FRTMA", "d10-spsite-ope-frtma.devst.devcabu.jp"),
  opePtaca("OPE_PTACA", "d10-spsite-ope-ptaca.devst.devcabu.jp"),
  // Itb
  dua2a("DUA2A", "d10-spsite-dua2a.devst.devcabu.jp"),
  dua2b("DUA2B", "d10-spsite-dua2b.devst.devcabu.jp"),
  a("A", "d10-spsite-a.devst.devcabu.jp"),
  sorpe("SORPE", "d10-spsite-sorpe.devst.devcabu.jp"),
  sorpf("SORPF", "d10-spsite-sorpf.devst.devcabu.jp"),
  sorpg("SORPG", "d10-spsite-sorpg.devst.devcabu.jp"),
  sorph("SORPH", "d10-spsite-sorph.devst.devcabu.jp"),
  putia("PUTIA", "d10-spsite-putia.devst.devcabu.jp"),
  frtma("FRTMA", "d10-spsite-frtma.devst.devcabu.jp"),
  ptaca("PTACA", "d10-spsite-ptaca.devst.devcabu.jp"),
  spetb("SPETB", "d10-spsite-spetb.devst.devcabu.jp"),
  kcesa("KCESA", "d10-spsite-kcesa.devst.devcabu.jp"),
  as01a("AS01A", "d10-spsite-as01a.devst.devcabu.jp"),
  as01b("AS01B", "d10-spsite-as01b.devst.devcabu.jp"),
  as01c("AS01C", "d10-spsite-as01c.devst.devcabu.jp"),
  as01e("AS01E", "d10-spsite-as01e.devst.devcabu.jp"),
  nnisb("NNISB", "d10-spsite-nnisb.devst.devcabu.jp"),
  ammfa("AMMFA", "d10-spsite-ammfa.devst.devcabu.jp"),
  ;

  final String value;
  final String domain;
  const StType(this.value, this.domain);

  static StType from(String? value) {
    return StType.values.firstWhere(
      (element) => element.value == value,
      orElse: () => StType.a,
    );
  }
}

enum DevType {
  // Dev2 Stub
  aStub("A_STUB", "kcmsr.dev.guide.inc"),
  fStub("F_STUB", "f-stub.kcmsr.dev.guide.inc"),
  gStub("G_STUB", "g-stub.kcmsr.dev.guide.inc"),
  iStub("I_STUB", "i-stub.kcmsr.dev.guide.inc"),
  jStub("J_STUB", "j-stub.kcmsr.dev.guide.inc"),
  ftdpdStub("FTDPD_STUB", "ftdpd-stub.kcmsr.dev.guide.inc"),
  putibStub("PUTIB_STUB", "putib-stub.kcmsr.dev.guide.inc"),
  guide1Stub("GUIDE1_STUB", "guide1-stub.kcmsr.dev.guide.inc"),
  guide2Stub("GUIDE2_STUB", "guide2-stub.kcmsr.dev.guide.inc"),
  guide3Stub("GUIDE3_STUB", "guide3-stub.kcmsr.dev.guide.inc"),
  guide4Stub("GUIDE4_STUB", "guide4-stub.kcmsr.dev.guide.inc"),
  guide5Stub("GUIDE5_STUB", "guide5-stub.kcmsr.dev.guide.inc"),
  // Dev2 ITA
  aIta("BFF", "bff.kcmsr.dev.guide.inc"),
  fIta("F_ITA", "f-ita.kcmsr.dev.guide.inc"),
  gIta("G_ITA", "g-ita.kcmsr.dev.guide.inc"),
  jIta("J_ITA", "j-ita.kcmsr.dev.guide.inc"),
  ftdpdIta("FTDPD_ITA", "ftdpd-ita.kcmsr.dev.guide.inc"),
  putibIta("PUTIB_ITA", "putib-ita.kcmsr.dev.guide.inc"),
  guide1Ita("GUIDE1_ITA", "guide1-ita.kcmsr.dev.guide.inc"),
  guide2Ita("GUIDE2_ITA", "guide2-ita.kcmsr.dev.guide.inc"),
  guide3Ita("GUIDE3_ITA", "guide3-ita.kcmsr.dev.guide.inc"),
  guide4Ita("GUIDE4_ITA", "guide4-ita.kcmsr.dev.guide.inc"),
  guide5Ita("GUIDE5_ITA", "guide5-ita.kcmsr.dev.guide.inc"),
  // Dev2 ope
  opeBFF("OPE_BFF", "ope-bff.kcmsr.dev.guide.inc"),
  // ST Stub
  stAStub("ST_A_STUB", "st.a-stub.kcmsr.dev.guide.inc"),
  dua2Stub("DUA2_STUB", "st.dua2a-stub.kcmsr.dev.guide.inc"),
  dua2bStub("DUA2B_STUB", "st.dua2b-stub.kcmsr.dev.guide.inc"),
  stSorpeStub("ST_SORPE_STUB", "st.sorpe-stub.kcmsr.dev.guide.inc"),
  stPutiaStub("ST_PUTIA_STUB", "st.putia-stub.kcmsr.dev.guide.inc"),
  stFrtmaStub("ST_FRTMA_STUB", "st.frtma-stub.kcmsr.dev.guide.inc"),
  stPtacaStub("ST_PTACA_STUB", "st.ptaca-stub.kcmsr.dev.guide.inc"),
  stGuide1Stub("ST_GUIDE1_STUB", "st.guide1-stub.kcmsr.dev.guide.inc"),
  stGuide2Stub("ST_GUIDE2_STUB", "st.guide2-stub.kcmsr.dev.guide.inc"),
  // ST ITA
  stAIta("ST_A_ITA", "st.a-ita.kcmsr.dev.guide.inc"),
  stDua2aIta("DUA2A", "st.dua2a-ita.kcmsr.dev.guide.inc"),
  stDua2bIta("DUA2B", "st.dua2b-ita.kcmsr.dev.guide.inc"),
  stSorpeIta("SORPE_ITA", "st.sorpe-ita.kcmsr.dev.guide.inc"),
  stPutiaIta("ST_PUTIA_ITA", "st.putia-ita.kcmsr.dev.guide.inc"),
  stFrtmaIta("ST_FRTMA_ITA", "st.frtma-ita.kcmsr.dev.guide.inc"),
  stPtacaIta("ST_PTACA_ITA", "st.ptaca-ita.kcmsr.dev.guide.inc"),
  stGuide1Ita("GUIDE1_ST", "st.guide1-ita.kcmsr.dev.guide.inc"),
  stGuide2Ita("ST_GUIDE2_ITA", "st.guide2-ita.kcmsr.dev.guide.inc"),
  ;

  final String value;
  final String domain;
  const DevType(this.value, this.domain);
  static DevType from(String? value) {
    return DevType.values.firstWhere(
      (element) => element.value == value,
      orElse: () => DevType.aStub,
    );
  }
}

enum AuthenticatorType {
  oob("oob"),
  otp("otp"),
  recoveryCode("recovery-code");

  final String value;
  const AuthenticatorType(this.value);

  static AuthenticatorType? from(String? value) {
    return AuthenticatorType.values.firstWhereOrNull(
      (element) => element.value == value,
    );
  }
}

enum OobChannel {
  auth0("auth0"),
  email("email");

  final String value;
  const OobChannel(this.value);

  static OobChannel? from(String? value) {
    return OobChannel.values.firstWhereOrNull(
      (element) => element.value == value,
    );
  }
}

enum ChallengeType {
  oob("oob"),
  otp("otp");

  final String value;
  const ChallengeType(this.value);

  static ChallengeType? from(String? value) {
    return ChallengeType.values.firstWhereOrNull(
      (element) => element.value == value,
    );
  }
}

enum GrantType {
  oob("http://auth0.com/oauth/grant-type/mfa-oob"),
  otp("http://auth0.com/oauth/grant-type/mfa-otp"),
  recovery("http://auth0.com/oauth/grant-type/mfa-recovery-code");

  final String value;
  const GrantType(this.value);

  static GrantType? from(String? value) {
    return GrantType.values.firstWhereOrNull(
      (element) => element.value == value,
    );
  }
}
