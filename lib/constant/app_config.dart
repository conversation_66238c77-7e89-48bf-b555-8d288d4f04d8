import 'package:flutter/foundation.dart';
import 'package:kc_member_site_native/main.dart';

import '../model/app_config_model.dart';
import 'base.dart';

const appConfigJson = {
  "app_config": {
    "wl": {
      "domain": [
        "s10-a.dev2.devcabu.jp",
        "s20-a.dev2.devcabu.jp",
        "s20-e0.dev2.devcabu.jp",
        "m-a.dev2.devcabu.jp",
        "s20-si0-a.dev2.devcabu.jp",
        "s20-si1-a.dev2.devcabu.jp",
        "spmobcon.devcabu.jp",
        "kf-a.dev2.devcabu.jp",
        "tl-a.dev2.devcabu.jp",
        "gweb.tfxclick.com",
        "apl.morningstar.co.jp",
        "qa.kabu.co.jp",
        "bk02.jibunbank.co.jp",
        "ib.jibunbank.co.jp",
        "a.t.webtracker.jp",
        "ssl.webtracker.jp",
        "i.t.webtracker.jp",
        "chart.ever.devcabu.jp",
        "prodacc-login-ma-a.devst.devcabu.jp",
        "kcmsr.dev.guide.inc",
        "bff.kcmsr.dev.guide.inc",
        "d10-spsite-a.dev2.devcabu.jp",
        "d10-spsite-f.dev2.devcabu.jp",
        "d10-spsite-g.dev2.devcabu.jp",
        "d10-spsite-h.dev2.devcabu.jp",
        "s20-e0.dev2.devcabu.jp",
        "s20-si0-e0.dev2.devcabu.jp",
        "s20-si1-e0.dev2.devcabu.jp",
        "s20-si0-f.dev2.devcabu.jp",
        "s20-si1-f.dev2.devcabu.jp",
        "s20-si0-g.dev2.devcabu.jp",
        "s20-si1-g.dev2.devcabu.jp",
        "s20-si0-h.dev2.devcabu.jp",
        "s20-si1-h.dev2.devcabu.jp",
        "s10.kabu.co.jp",
        "s20.kabu.co.jp",
        "m.kabu.co.jp",
        "s20.si0.kabu.co.jp",
        "s20.si1.kabu.co.jp",
        "spmobcon.kabu.co.jp",
        "kf.kabu.co.jp",
        "tl.kabu.co.jp",
        "chart.ever.kabu.co.jp",
        "prodacc-login-ma.kabu.co.jp",
        "d10-spsite.kabu.co.jp",
        "ktid.karte-edge.io",
        "mauth-sso-a.dev2.devcabu.jp",
        "mauth-sso-f.dev2.devcabu.jp",
        "mauth-sso-g.dev2.devcabu.jp",
        "mauth-sso-h.dev2.devcabu.jp"
      ]
    },
    "openwithbrowser": {
      "urlincludes": [
        "/iphone/appmenu/MenuCushion4TfxSso_",
        "/iphone/appmenu/MenuCushion4TcfdSso_",
        "www.jibunbank.co.jp",
        "m.aukabucom.chart.ever",
        "/ap/iPhone/Personal/HenkouDenshiMoushikomi",
        "/iphone/personal/creditCard/Cushion4SaveCardInput.asp",
        "/iphone/personal/creditCard/Cushion4DeleteCardInput.asp",
        "/iphone/personal/creditCard/Cushion4TsumitateMenu.asp",
        "/ap/iPhone/Personal/SaveCard/Input",
        "/ap/iphone/Personal/DeleteCard/Input",
        "/iphone/personal/auID/auIDRegister.asp",
        "/iPhone/personal/auID/auIDRuleacceptAppsm.asp",
        "/iphone/Trade/CashIn/CI0E101.asp",
        "/iphone/Trade/CashIn/JibunKessai/cik0101.asp",
        "/ap/iPhone/Cash/Yucho/Pay/Input",
        "/ap/iphone/personal/RealTransferApply/Confirm",
        "/ap/iphone/personal/MultiBankDepositApply/Input",
        "/iPhone/personal/dkstatus/jbn1001.asp",
        "/ap/pc/",
        "/members/",
        "/iphone/appmenu/MenuCushion4TfxSso_Price.asp",
        "/iphone/appmenu/MenuCushion4TcfdSso_Price.asp",
        "redirect_robo_marginroboflow.asp"
      ]
    }
  }
};

late AppConfigModel appConfigModel;

void initAppConfigJson({Map<String, dynamic>? config}) {
  final appConfig = config ?? appConfigJson;
  appConfigModel = AppConfigModel.fromJson(appConfig);
  debugPrint(appConfigModel.toString());
}

Future getAppConfig() async {
  final url = '${Base.configurationFileUrl}/.well-known/GenerateConfig';
  try {
    final response = await kcmApiClient.get(
      Uri.parse(url),
      baseUrl: null,
    );
    if (response.statusCode == 200) {
      initAppConfigJson(config: response.body);
    } else {
      throw Exception('Failed to get app config');
    }
  } catch (_) {}
}
