// ignore_for_file: use_build_context_synchronously

import 'package:auto_route/auto_route.dart';
import 'package:collection/collection.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter_neumorphic/flutter_neumorphic.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/constant/base.dart';
import 'package:kc_member_site_native/constant/enum.dart';
import 'package:kc_member_site_native/extensions/build_context_extensions.dart';
import 'package:kc_member_site_native/model/login_model.dart';
import 'package:kc_member_site_native/model/mfa_model.dart';
import 'package:kc_member_site_native/routes/app_router.dart';
import 'package:kc_member_site_native/service/login_service.dart';
import 'package:kc_member_site_native/util/app_dialog.dart';
import 'package:kc_member_site_native/util/app_tooltip.dart';
import 'package:kc_member_site_native/util/in_app_browser.dart';
import 'package:kc_member_site_native/view_model/base_view_model.dart';
import 'package:kc_member_site_native/view_model/login_view_model.dart';
import 'package:provider/provider.dart';

/// Use for mfa_recovery_input
class MfaRecoveryViewModel extends BaseViewModel {
  final LoginService loginService;
  MfaRecoveryViewModel({required this.loginService});
  String _recoveryCode = "";
  bool get hasRecoveryCode => _recoveryCode.isNotEmpty;
  bool _isDispose = false;

  late BuildContext _tooltipContext;
  void setTooltipContext(BuildContext tooltipContext) {
    debugPrint("setTooltipContext");
    _tooltipContext = tooltipContext;
  }

  bool _isVerifying = false;
  bool get isVerifying => _isVerifying;
  List<MfaAuthenticatorsModel>? _mfaAuth;

  @override
  void initData() {
    super.initData();
    _authMfaAuthenticators();
  }

  void _setVerifying(bool value) {
    _isVerifying = value;
    if (_isDispose) return;
    notifyListeners();
  }

  Future<void> _authMfaAuthenticators() async {
    final loginVm = context.read<LoginViewModel>();
    final mfaAuthRes = await loginService.authMfaAuthenticators(
      ck: loginVm.cookiesLite,
    );
    if (_isDispose) return;

    if (mfaAuthRes.statusCode == 401) {
      await DialogUtils.showDl04(context, context.l10n.me11);
      context.replaceRoute(LoginRoute());
      return;
    }
    if (mfaAuthRes.data == null) {
      await DialogUtils.showDl04(context, context.l10n.me03);
      context.replaceRoute(LoginRoute());
      return;
    }
    _mfaAuth = mfaAuthRes.data;
    if (_mfaAuth?.firstWhereOrNull((e) => e.isMfaRecovery) == null) {
      context.replaceRoute(const MfaSelectRoute());
    }
  }

  void onChangedRecoveryCode(String value) {
    _recoveryCode = value;
    EasyDebounce.debounce('onChangedRecoveryCode', const Duration(milliseconds: 500), () {
      notifyListeners();
    });
  }

  Future<void> send() async {
    if (_isDispose) return;
    _setVerifying(true);
    final loginVm = context.read<LoginViewModel>();

    final mfaOauthToken = await loginService.authMfaOauthToken(
      ck: loginVm.cookiesLite,
      grantType: GrantType.recovery,
      recoveryCode: _recoveryCode,
    );
    if (_isDispose) return;
    if (mfaOauthToken.isSuccessful) {
      context.replaceRoute(MfaRecoveryRoute(
        newRecoveryCode: mfaOauthToken.recovery_code ?? "",
        mfaOauthToken: mfaOauthToken,
      ));
      return;
    }
    _setVerifying(false);

    // Error handle
    switch (mfaOauthToken.error?.errorCode) {
      case ErrorCode.authRejected:
        TooltipUtils().createTooltip(context.l10n.incorrectRecoveryCode, 10.w).show(_tooltipContext);
        return;
      case ErrorCode.tooManyFailCode:
        TooltipUtils().createTooltip(context.l10n.maximumNumberAttemptsTryAgainLater, 10.w).show(_tooltipContext);
        return;
      default:
        break;
    }
    if (mfaOauthToken.error?.httpStatus == 401 ||
        [
          ErrorCode.expiredToken,
        ].contains(mfaOauthToken.error?.errorCode)) {
      await DialogUtils.showDl04(context, context.l10n.me11);
      context.replaceRoute(LoginRoute());
      return;
    }
    await DialogUtils.showDl04(context, context.l10n.me03);
    context.replaceRoute(LoginRoute());
  }

  void authAnotherMethod() {
    _isDispose = true;
    debugPrint("authAnotherMethod");
    context.replaceRoute(const MfaSelectRoute());
  }

  void issueRecoveryCode() => launchUrlString(Base.recoveryCodeResetUrl);

  void howToIssueRecoveryCode() => launchUrlString(Base.recoveryCodeIssueMethodUrl);

  @override
  void dispose() {
    _isDispose = true;
    debugPrint("___dispose VM");
    super.dispose();
  }
}
