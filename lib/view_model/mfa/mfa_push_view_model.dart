// ignore_for_file: use_build_context_synchronously

import 'package:auto_route/auto_route.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_neumorphic/flutter_neumorphic.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/constant/enum.dart';
import 'package:kc_member_site_native/extensions/build_context_extensions.dart';
import 'package:kc_member_site_native/model/login_model.dart';
import 'package:kc_member_site_native/routes/app_router.dart';
import 'package:kc_member_site_native/service/login_service.dart';
import 'package:kc_member_site_native/service/secure_storage_service.dart';
import 'package:kc_member_site_native/util/app_dialog.dart';
import 'package:kc_member_site_native/util/app_tooltip.dart';
import 'package:kc_member_site_native/util/network_error.dart';
import 'package:kc_member_site_native/view_model/base_view_model.dart';
import 'package:kc_member_site_native/view_model/login_view_model.dart';
import 'package:kc_member_site_native/widgets/kc_dropdown.dart';
import 'package:provider/provider.dart';

class MfaPushViewModel extends BaseViewModel {
  final LoginService loginService;
  final SecureStorageService secureStorage;
  MfaPushViewModel({
    required this.loginService,
    required this.secureStorage,
  });
  bool _isDispose = false;
  List<ItemOption<String>> _deviceList = [];
  List<ItemOption<String>> get deviceList => _deviceList;
  String? _deviceSelected;

  bool _isBusy = true;
  bool get isBusy => _isBusy;

  String _pollingId = "";

  /// authenticator_id
  String? get deviceSelected => _deviceSelected;

  @override
  void initData() async {
    try {
      await _initDeviceList();
      await _resendNotification();
      _isBusy = false;
      notifyListeners();
    } catch (e) {
      // nothing
    }
  }

  Future<void> _initDeviceList() async {
    final loginVm = context.read<LoginViewModel>();
    final mfaAuthRes = await loginService.authMfaAuthenticators(
      ck: loginVm.cookiesLite,
    );
    if (_isDispose) return;
    if (mfaAuthRes.statusCode == 401) {
      await DialogUtils.showDl04(context, context.l10n.me11);
      context.replaceRoute(LoginRoute());
      throw "error 401";
    }
    if (mfaAuthRes.data == null) {
      await DialogUtils.showDl04(context, context.l10n.me03);
      context.replaceRoute(LoginRoute());
      throw "no data";
    }
    final mfaAuth0 = mfaAuthRes.data!.where((e) => e.isMfaPush);
    if (mfaAuth0.isEmpty) {
      context.replaceRoute(const MfaSelectRoute());
      throw "mfaAuth0.isEmpty";
    }
    _deviceList = mfaAuth0
        .map((e) => ItemOption(
              name: e.name ?? "-",
              value: e.id ?? "",
            ))
        .toList();

    final initValue = _deviceSelected ?? await secureStorage.getAuthenticatorId();
    final oldDevice = _deviceList.firstWhereOrNull((e) => e.value == initValue);
    _deviceSelected = oldDevice != null ? oldDevice.value : (_deviceList.isEmpty ? "" : _deviceList[0].value);
    if (isDispose) return;
    notifyListeners();
  }

  Future<void> _authMfaOauthToken({
    required CookiesModel? ck,
    required BuildContext context,
    String? oobCode,

    /// Id for stop polling
    required String id,
  }) async {
    if (id != _pollingId) {
      return;
    }
    if (_isDispose) return;
    final mfaOauthToken = await loginService.authMfaOauthToken(
      ck: ck,
      grantType: GrantType.oob,
      oobCode: oobCode,
    );
    if (_isDispose) return;
    if (mfaOauthToken.isSuccessful) {
      secureStorage.setAuthenticatorId(_deviceSelected);
      context.read<LoginViewModel>().postLoginProcessing(context, mfaOauthToken);
      return;
    }
    await mfaErrorHandle(
      error: mfaOauthToken.error,
      context: context,
      onRetry: () => _authMfaOauthToken(
        id: id,
        ck: ck,
        context: context,
        oobCode: oobCode,
      ),
    );
  }

  void _renewPollingId() {
    _pollingId = DateTime.now().toIso8601String();
  }

  Future<void> _resendNotification({
    BuildContext? toastContext,
  }) async {
    debugPrint("resendNotification");
    final loginVm = context.read<LoginViewModel>();
    final mfaChallenge = await loginService.authMfaChallenge(
      ck: loginVm.cookiesLite,
      challengeType: ChallengeType.oob,
      authenticatorId: _deviceSelected,
    );
    if (_isDispose) return;
    if (mfaChallenge.isSuccessful) {
      if (toastContext != null) {
        TooltipUtils().showToast(
          toastContext: toastContext,
          message: context.l10n.notificationHasBeenResent,
          arrowTipDistance: 23.w,
        );
      }
      _renewPollingId();
      _authMfaOauthToken(
        id: _pollingId,
        ck: loginVm.cookiesLite,
        context: context,
        oobCode: mfaChallenge.oob_code,
      );
      return;
    }
    final error = mfaChallenge.error;
    if (error?.httpStatus == 401 || error?.errorCode == ErrorCode.expiredToken) {
      await DialogUtils.showDl04(context, context.l10n.me11);
      context.replaceRoute(LoginRoute());
      return;
    }
    switch (error?.errorCode) {
      case ErrorCode.tooManyPushNotify:
        DialogUtils.showDl04(context, context.l10n.me12);
        return;
      default:
        await DialogUtils.showDl04(context, context.l10n.me15);
        return;
    }
  }

  Future<void> resendNotification(BuildContext toastContext) async {
    _isBusy = true;
    notifyListeners();
    await _resendNotification(toastContext: toastContext);
    _isBusy = false;
    if (isDispose) return;
    notifyListeners();
  }

  void authWithCode() {
    _isDispose = true;
    debugPrint("authWithCode");
    context.replaceRoute(const MfaTotpRoute());
  }

  void authAnotherMethod() {
    debugPrint("authAnotherMethod");
    _isDispose = true;
    context.replaceRoute(const MfaSelectRoute());
  }

  void onDeviceSelected(String value) {
    debugPrint("onDeviceSelected $value");
    _deviceSelected = value;
    notifyListeners();
    initData();
  }

  @override
  void dispose() {
    _isDispose = true;
    debugPrint("___dispose VM");
    super.dispose();
  }
}
