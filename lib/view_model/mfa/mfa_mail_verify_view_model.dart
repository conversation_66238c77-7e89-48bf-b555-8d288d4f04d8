// ignore_for_file: use_build_context_synchronously

import 'package:auto_route/auto_route.dart';
import 'package:kc_member_site_native/constant/base.dart';
import 'package:kc_member_site_native/extensions/build_context_extensions.dart';
import 'package:kc_member_site_native/model/mfa_model.dart';
import 'package:kc_member_site_native/routes/app_router.dart';
import 'package:kc_member_site_native/service/login_service.dart';
import 'package:kc_member_site_native/service/polling_service.dart';
import 'package:kc_member_site_native/util/app_dialog.dart';
import 'package:kc_member_site_native/util/in_app_browser.dart';
import 'package:kc_member_site_native/view_model/base_view_model.dart';
import 'package:kc_member_site_native/view_model/login_view_model.dart';
import 'package:provider/provider.dart';

class MfaMailVerifyViewModel extends BaseViewModel {
  final LoginService loginService;
  final PollingService pollingService;

  MfaMailVerifyViewModel({
    required this.loginService,
    required this.pollingService,
  });

  List<MfaAuthenticatorsModel>? _mfaAuthRes;
  bool _hasResendEmail = false;
  bool get hasResendEmail => _hasResendEmail;

  @override
  void initData() {
    _startPolling();
  }

  void _startPolling() {
    pollingService.polling(() => _mfaAuthenticators(), onResult: (result) async {
      if (result?.isEmpty ?? true) return;
      for (final mfaAuth in result!) {
        if (mfaAuth.isMfaEmail) {
          pollingService.cancel();
          _showSettingComplete();
          return;
        }
      }
    }, seconds: 5);
  }

  Future<void> _showSettingComplete() async {
    await DialogUtils.showComplete(context, message: context.l10n.settingsCompleted, closeAfter: 5);
    _nextScreenDeterminationProcess();
  }

  @override
  void dispose() {
    super.dispose();
    pollingService.cancel();
    DialogUtils.isShowingDl04 = false; // Ensure reset this flag
  }

  Future<List<MfaAuthenticatorsModel>?> _mfaAuthenticators() async {
    final loginVm = context.read<LoginViewModel>();
    final res = await loginService.authMfaAuthenticators(
      ck: loginVm.cookiesLite,
    );
    _mfaAuthRes = res.data;
    if (res.statusCode == 401) {
      if (DialogUtils.isShowingDl04) return null;
      pollingService.cancel();
      await DialogUtils.showDl04(context, context.l10n.me11);
      context.replaceRoute(LoginRoute());
    } else if (res.data == null) {
      if (DialogUtils.isShowingDl04) return null;
      pollingService.cancel();
      await DialogUtils.showDl04(context, context.l10n.me03);
      context.replaceRoute(LoginRoute());
    }
    return _mfaAuthRes;
  }

  void nextScreenDeterminationProcess() async {
    pollingService.cancel();
    _nextScreenDeterminationProcess();
  }

  void _nextScreenDeterminationProcess() async {
    final mfaType = loginService.checkPreferredMfaType(_mfaAuthRes ?? []);

    if (mfaType.isMfaPush) {
      context.replaceRoute(const MfaPushRoute());
      return;
    }
    if (mfaType.isMfaEmail) {
      context.replaceRoute(const MfaEmailRoute());
      return;
    }
    if (mfaType.isMfaRecovery) {
      context.replaceRoute(const MfaRecoveryInputRoute());
      return;
    }
    if (DialogUtils.isShowingDl04) return null;
    await DialogUtils.showDl04(context, context.l10n.me13);
    context.replaceRoute(LoginRoute());
  }

  Future<void> mfaEmailResend() async {
    final loginVm = context.read<LoginViewModel>();
    final res = await loginService.authMfaEmailResend(ck: loginVm.cookiesLite);
    if (res.statusCode == 200) {
      _hasResendEmail = true;
      notifyListeners();
      return;
    }
    if (DialogUtils.isShowingDl04) return;
    if (res.statusCode == 401) {
      pollingService.cancel();
      await DialogUtils.showDl04(context, context.l10n.me11);
      context.replaceRoute(LoginRoute());
      return;
    }
    if (res.statusCode == 400 && res.data?.error == "invalid_email_address") {
      await DialogUtils.showDl04(context, context.l10n.invalidEmailError);
      return;
    }
    await DialogUtils.showDl04(context, context.l10n.me03);
  }

  void onTapHowToChangeYourAuthEmail() => launchUrlString(Base.howToChangeAuthEmailUrl);

  void onTapWhatHappenIfNoVerifyEmail() => launchUrlString(Base.whatHappenIfNoAuthUrl);
}
