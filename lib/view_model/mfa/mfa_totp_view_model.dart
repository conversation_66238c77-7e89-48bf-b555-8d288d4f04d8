// ignore_for_file: use_build_context_synchronously

import 'package:auto_route/auto_route.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_neumorphic/flutter_neumorphic.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/constant/enum.dart';
import 'package:kc_member_site_native/extensions/build_context_extensions.dart';
import 'package:kc_member_site_native/model/login_model.dart';
import 'package:kc_member_site_native/model/mfa_model.dart';
import 'package:kc_member_site_native/routes/app_router.dart';
import 'package:kc_member_site_native/service/login_service.dart';
import 'package:kc_member_site_native/util/app_dialog.dart';
import 'package:kc_member_site_native/util/app_tooltip.dart';
import 'package:kc_member_site_native/view_model/base_view_model.dart';
import 'package:kc_member_site_native/view_model/login_view_model.dart';
import 'package:provider/provider.dart';

/// Use for mfa_totp & mfa_email
class MfaTotpViewModel extends BaseViewModel {
  MfaTotpViewModel({
    required this.loginService,
    required this.challengeType,
  });
  final LoginService loginService;
  final ChallengeType challengeType;

  List<MfaAuthenticatorsModel>? _mfaAuth;

  bool _isDispose = false;
  bool _isBusy = true;
  bool get isBusy => _isBusy;
  bool _isVerifying = false;
  bool get isVerifying => _isVerifying;
  String _email = "";
  String get email => _email;

  late BuildContext _tooltipContext;
  void setTooltipContext(BuildContext tooltipContext) {
    debugPrint("setTooltipContext");
    _tooltipContext = tooltipContext;
  }

  @override
  void initData() async {
    super.initData();
    try {
      await _authMfaAuthenticators();
      await _authMfaChallenge();
    } catch (e) {
      // nothing
    } finally {
      _setBusy(false);
    }
  }

  void _setVerifying(bool value) {
    _isVerifying = value;
    if (_isDispose) return;
    notifyListeners();
  }

  void _setBusy(bool value) {
    _isBusy = value;
    if (_isDispose) return;
    notifyListeners();
  }

  Future<void> _authMfaAuthenticators() async {
    final loginVm = context.read<LoginViewModel>();
    final mfaAuthRes = await loginService.authMfaAuthenticators(
      ck: loginVm.cookiesLite,
    );
    if (_isDispose) throw "disposed";
    if (mfaAuthRes.statusCode == 401) {
      await DialogUtils.showDl04(context, context.l10n.me11);
      context.replaceRoute(LoginRoute());
      throw "error 401";
    }
    if (mfaAuthRes.data == null) {
      await DialogUtils.showDl04(context, context.l10n.me03);
      context.replaceRoute(LoginRoute());
      throw "no data";
    }
    _mfaAuth = mfaAuthRes.data;
    final currentAuth = _getCurrentAuth(_mfaAuth);
    if (currentAuth == null) {
      context.replaceRoute(const MfaSelectRoute());
      throw "no valid";
    }
  }

  MfaChallengeModel? _mfaChallenge;
  Future<bool> _authMfaChallenge() async {
    final currentAuth = _getCurrentAuth(_mfaAuth);
    if (challengeType == ChallengeType.oob) {
      // Email screen
      _email = currentAuth?.name ?? "-";
      notifyListeners();
    }
    final loginVm = context.read<LoginViewModel>();
    final mfaChallenge = await loginService.authMfaChallenge(
      ck: loginVm.cookiesLite,
      challengeType: challengeType,
      authenticatorId: currentAuth?.id,
    );
    _mfaChallenge = mfaChallenge;
    if (mfaChallenge.isSuccessful) {
      return true;
    }
    final error = mfaChallenge.error;
    if (_isDispose) return false;
    if (error?.httpStatus == 401 || error?.errorCode == ErrorCode.expiredToken) {
      await DialogUtils.showDl04(context, context.l10n.me11);
      context.replaceRoute(LoginRoute());
      return false;
    }
    switch (error?.errorCode) {
      case ErrorCode.tooManyEmail:
        await DialogUtils.showDl04(context, context.l10n.me14);
        break;
      default:
        await DialogUtils.showDl04(context, context.l10n.me03);
        context.replaceRoute(LoginRoute());
        break;
    }
    return false;
  }

  Future<void> _authMfaOauthToken({
    required CookiesModel? ck,
    required BuildContext context,
    required GrantType grantType,
    required TextEditingController teController,
    String? otp,
    String? oobCode,
    String? bindingCode,
  }) async {
    if (_isDispose) return;
    _setVerifying(true);
    final mfaOauthToken = await loginService.authMfaOauthToken(
      ck: ck,
      grantType: grantType,
      otp: otp,
      oobCode: oobCode,
      bindingCode: bindingCode,
    );
    if (_isDispose) return;
    if (mfaOauthToken.isSuccessful) {
      await context.read<LoginViewModel>().postLoginProcessing(context, mfaOauthToken);
      _setVerifying(false);
      return;
    }
    teController.clear();
    _setVerifying(false);

    // Error handle
    switch (mfaOauthToken.error?.errorCode) {
      case ErrorCode.invalidOtp:
      case ErrorCode.invalidBindingCode:
        TooltipUtils().createTooltip(context.l10n.codeIncorrect).show(_tooltipContext);
        return;
      case ErrorCode.tooManyFailCode:
        TooltipUtils().createTooltip(context.l10n.maximumNumberAttemptsTryAgainLater).show(_tooltipContext);
        return;
      default:
        break;
    }
    if (mfaOauthToken.error?.httpStatus == 401 ||
        [
          ErrorCode.expiredToken,
        ].contains(mfaOauthToken.error?.errorCode)) {
      await DialogUtils.showDl04(context, context.l10n.me11);
      context.replaceRoute(LoginRoute());
      return;
    }
    await DialogUtils.showDl04(context, context.l10n.me03);
    context.replaceRoute(LoginRoute());
  }

  MfaAuthenticatorsModel? _getCurrentAuth(List<MfaAuthenticatorsModel>? mfaAuth) {
    if (challengeType == ChallengeType.otp) {
      return mfaAuth?.firstWhereOrNull((e) => e.isMfaOtp);
    }
    if (challengeType == ChallengeType.oob) {
      return mfaAuth?.firstWhereOrNull((e) => e.isMfaEmail);
    }
    return null;
  }

  Future<void> onCompletedEnterOtp({
    required String otp,
    required TextEditingController teController,
  }) async {
    _setVerifying(true);
    debugPrint("onCompletedEnterOtp $otp");
    final loginVm = context.read<LoginViewModel>();
    if (challengeType == ChallengeType.oob) {
      // Email
      await _authMfaOauthToken(
        ck: loginVm.cookiesLite,
        context: context,
        oobCode: _mfaChallenge?.oob_code,
        grantType: GrantType.oob,
        bindingCode: otp,
        teController: teController,
      );
    } else {
      // Totp
      await _authMfaOauthToken(
        ck: loginVm.cookiesLite,
        context: context,
        otp: otp,
        grantType: GrantType.otp,
        teController: teController,
      );
    }
    _setVerifying(false);
  }

  void authAnotherMethod() {
    _isDispose = true;
    debugPrint("authAnotherMethod");
    context.replaceRoute(const MfaSelectRoute());
  }

  Future<void> onPressResendCode(BuildContext toastContext) async {
    _setBusy(true);
    final success = await _authMfaChallenge();
    if (success) {
      TooltipUtils().showToast(
        toastContext: toastContext,
        message: context.l10n.weHaveResentCode,
        arrowTipDistance: 26.w,
      );
    }
    _setBusy(false);
  }

  @override
  void dispose() {
    _isDispose = true;
    debugPrint("___dispose VM");
    super.dispose();
  }
}
