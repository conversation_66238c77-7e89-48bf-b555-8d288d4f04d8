// ignore_for_file: use_build_context_synchronously

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_neumorphic/flutter_neumorphic.dart';
import 'package:kc_member_site_native/extensions/build_context_extensions.dart';
import 'package:kc_member_site_native/model/mfa_model.dart';
import 'package:kc_member_site_native/model/mfa_type.dart';
import 'package:kc_member_site_native/routes/app_router.dart';
import 'package:kc_member_site_native/service/login_service.dart';
import 'package:kc_member_site_native/util/app_dialog.dart';
import 'package:kc_member_site_native/view_model/base_view_model.dart';
import 'package:kc_member_site_native/view_model/login_view_model.dart';
import 'package:provider/provider.dart';

class MfaSelectViewModel extends BaseViewModel {
  final LoginService loginService;
  MfaSelectViewModel({
    required this.loginService,
  });
  bool _isDispose = false;

  List<MfaAuthenticatorsModel> _mfaAuthRes = [];
  List<MfaAuthenticatorsModel> get mfaAuth => _mfaAuthRes;
  MfaType _mfaType = const MfaType();
  MfaType get mfaType => _mfaType;

  @override
  void initData() async {
    _authMfaAuthenticators();
  }

  Future<void> _authMfaAuthenticators() async {
    final loginVm = context.read<LoginViewModel>();
    final mfaAuthRes = await loginService.authMfaAuthenticators(
      ck: loginVm.cookiesLite,
    );
    if (_isDispose) return;
    if (mfaAuthRes.statusCode == 401) {
      await DialogUtils.showDl04(context, context.l10n.me11);
      context.replaceRoute(LoginRoute());
      return;
    }
    if (mfaAuthRes.data == null) {
      await DialogUtils.showDl04(context, context.l10n.me03);
      context.replaceRoute(LoginRoute());
      return;
    }
    _mfaAuthRes = mfaAuthRes.data!;
    for (final mfaAuth in _mfaAuthRes) {
      if (mfaAuth.isMfaPush) {
        _mfaType = _mfaType.copyWith(isMfaPush: true);
      }
      if (mfaAuth.isMfaEmail) {
        _mfaType = _mfaType.copyWith(isMfaEmail: true);
      }
      if (mfaAuth.isMfaRecovery) {
        _mfaType = _mfaType.copyWith(isMfaRecovery: true);
      }
    }
    if (_mfaType.isMfa) {
      notifyListeners();
      return;
    }
    // No element valid
    await DialogUtils.showDl04(context, context.l10n.me13);
    context.replaceRoute(LoginRoute());
  }

  @override
  void dispose() {
    _isDispose = true;
    debugPrint("___dispose VM");
    super.dispose();
  }
}
