// ignore_for_file: use_build_context_synchronously

import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_web_browser/flutter_web_browser.dart';
import 'package:kc_member_site_native/constant/base.dart';
import 'package:kc_member_site_native/extensions/build_context_extensions.dart';
import 'package:kc_member_site_native/routes/app_router.dart';
import 'package:kc_member_site_native/screen/webview_screen.dart';
import 'package:kc_member_site_native/service/index.dart';
import 'package:kc_member_site_native/util/app_dialog.dart';
import 'package:kc_member_site_native/util/crypto.dart';
import 'package:kc_member_site_native/util/in_app_browser.dart';
import 'package:kc_member_site_native/util/uni_links.dart';
import 'package:kc_member_site_native/view_model/base_view_model.dart';
import 'package:kc_member_site_native/view_model/login_view_model.dart';
import 'package:provider/provider.dart';

class WebviewViewModel extends BaseViewModel {
  WebviewViewModel(this._loginService);
  final LoginService _loginService;

  String _code = "";
  String _nativeState = "";

  String _generateCodeVerifier() {
    return Crypto.randomHexString(32);
  }

  @override
  void initData() {
    super.initData();
    UniLinksUtil().initUniLinks(_handleOpenLink);
  }

  @override
  void dispose() {
    UniLinksUtil().unsubscribe();
    super.dispose();
  }

  void _handleOpenLink(Uri uri) async {
    debugPrint("_handleOpenLink $uri");
    final urlString = uri.toString();

    if (!(urlString.contains(Base.loginCompleteSuffix) ||
        urlString.contains(Base.mfaLoginCompleteSuffix) ||
        urlString.startsWith(Base.customUrlScheme))) {
      return;
    }
    mainWebViewController?.loadRequest(Uri.parse(Base.securitySettingTopUrl));
    if (Platform.isAndroid) {
      // fix: KCMSR-6762 (prevent web invoke onForeground)
      await Future.delayed(Duration.zero);
    }
    FlutterWebBrowser.close();
  }

  Future<bool> _getCodeRegistration() async {
    final loginVm = context.read<LoginViewModel>();
    _code = _generateCodeVerifier();
    _nativeState = _generateCodeVerifier();

    final res = await _loginService.authCodeRegistration(
      nativeState: _nativeState,
      code: _code,
      ck: loginVm.cookiesLite,
    );
    if (isDispose) return false;
    switch (res.statusCode) {
      case 200:
        return true;
      case 401:
        await DialogUtils.showDl04(context, context.l10n.me00);
        context.replaceRoute(LoginRoute());
        return false;
      default:
        DialogUtils.showDl04(context, context.l10n.me03);
        return false;
    }
  }

  Future<void> handleSecuritySettingsLogin(String authUrl) async {
    final isSuccess = await _getCodeRegistration();
    if (!isSuccess || isDispose) {
      return;
    }
    openChromeSafariBrowser(
      context: context,
      url: "${Base.bffDomainUrl}${Base.authNativeSecurityLoginPaths[authUrl]}?code=$_code",
    );
  }
}
