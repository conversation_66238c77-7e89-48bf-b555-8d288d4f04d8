// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import "package:collection/collection.dart";
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:flutter/material.dart';
import 'package:flutter_neumorphic/flutter_neumorphic.dart';
import 'package:kc_member_site_native/constant/base.dart';
import 'package:kc_member_site_native/constant/storage.dart';
import 'package:kc_member_site_native/extensions/build_context_extensions.dart';
import 'package:kc_member_site_native/extensions/string_extensions.dart';
import 'package:kc_member_site_native/main.dart';
import 'package:kc_member_site_native/model/account_model.dart';
import 'package:kc_member_site_native/model/login_model.dart';
import 'package:kc_member_site_native/model/mfa_model.dart';
import 'package:kc_member_site_native/routes/app_router.dart';
import 'package:kc_member_site_native/service/account_service.dart';
import 'package:kc_member_site_native/service/login_service.dart';
import 'package:kc_member_site_native/service/secure_storage_service.dart';
import 'package:kc_member_site_native/util/app_dialog.dart';
import 'package:kc_member_site_native/util/cookie_manager.dart';
import 'package:kc_member_site_native/util/firebase_analytics.dart';
import 'package:kc_member_site_native/util/karte_util.dart';
import 'package:kc_member_site_native/util/network_error.dart';
import 'package:kc_member_site_native/util/run_javascript.dart';
import 'package:kc_member_site_native/util/shared_preference.dart';
import 'package:kc_member_site_native/view_model/base_view_model.dart';

class LoginViewModel extends BaseViewModel {
  final AccountService accountService;
  final LoginService loginService;

  final SecureStorageService secureStorage;
  LoginViewModel({
    required this.accountService,
    required this.loginService,
    required this.secureStorage,
  }) {
    getAccountInfo = Future.wait([secureStorage.getUsername(), secureStorage.getPassword()]);
  }
  late Future<List<String?>> getAccountInfo;

  bool _isAutoLogin = false;
  bool _isSaveUsername = false;
  bool _isSavePassword = false;

  String _username = "";
  String _password = "";

  String? _maintenanceAnnounce;

  bool get isAutoLogin => _isAutoLogin;
  bool get isSaveUsername => _isAutoLogin || _isSaveUsername;
  bool get isSavePassword => _isAutoLogin || _isSavePassword;
  String get username => _username;
  String get password => _password;
  bool get canLogin => _username.isNotEmpty && _password.isNotEmpty;

  String? get maintenanceAnnounce => _maintenanceAnnounce;

  bool isDisposed = false;
  @override
  void dispose() {
    isDisposed = true;
    super.dispose();
  }

  BuildContext get _context => context;
  bool _isSplash = false;
  set isSplash(bool value) => _isSplash = value;

  void init() async {
    _isAutoLogin = sharedPreferences.getBool(StorageKey.isAutoLogin) ?? false;
    _isSaveUsername = sharedPreferences.getBool(StorageKey.isSaveUsername) ?? false;
    _isSavePassword = sharedPreferences.getBool(StorageKey.isSavePassword) ?? false;

    if (_isAutoLogin) {
      _isSaveUsername = true;
      _isSavePassword = true;
    }
    final info = await getAccountInfo;
    _username = info[0] ?? "";
    _password = info[1] ?? "";
    notifyListeners();
  }

  void toggleAutoLogin(bool value) {
    _isAutoLogin = value;
    if (value) {
      _isSaveUsername = value;
      _isSavePassword = value;
    }
    notifyListeners();
  }

  void toggleUsername(bool value) {
    _isSaveUsername = value;
    notifyListeners();
  }

  void togglePassword(bool value) {
    _isSavePassword = value;
    notifyListeners();
  }

  void onChangeUsername(String value) {
    _username = value;
    notifyListeners();
  }

  void onChangePassword(String value) {
    _password = value;
    notifyListeners();
  }

  Future<void> _showDialogInNewContext(Future<void> Function() callback) async {
    if (_isSplash && isAutoLogin) {
      context.replaceRoute(LoginRoute());
      // Delay for update new context to show error dialog in Login page
      await Future.delayed(const Duration(milliseconds: 200), () {
        callback.call();
      });
      return;
    }
    await callback.call();
  }

  Future<void> loginProgress() async {
    if (_username.isEmpty || _password.isEmpty) {
      // ensure getAccountInfo done
      final info = await getAccountInfo;
      _username = info[0] ?? "";
      _password = info[1] ?? "";
    }

    final serviceAvailable = await loginService.getServiceAvailable();
    if (serviceAvailable?.isAvailable == false) {
      _showDialogInNewContext(() async {
        DialogUtils.showDl04(_context, serviceAvailable?.message ?? "");
      });
      return;
    }

    String? appCheckToken;
    try {
      appCheckToken = await FirebaseAppCheck.instance.getToken();
    } catch (e) {
      debugPrint(e.toString());
    }
    debugPrint("appCheckToken: $appCheckToken");
    final res = await loginService.spNativeLoginRop(
      username: _username,
      password: _password,
      appCheckToken: appCheckToken,
    );
    if (res.isSuccessful) {
      _cookiesLite = res.cookiesLite;
      await postLoginProcessing(context, res);
      return;
    }
    await _showDialogInNewContext(() => _loginErrorHandle(res));
  }

  /// https://gitbook.guide.inc/kcmsr/vn/Native/Login.html#ログイン後処理
  Future<String?> _checkAccountStatus(LoginModel res) async {
    final cookies = res.cookies ?? [];
    final syncCookie = _syncCookies(cookies);
    final ck = res.cookiesLite ?? const CookiesModel();
    debugPrint("___start checkAccountStatus");
    var responses = await Future.wait([
      guardRequest(() => accountService.getLoginStatus(ck)),
      guardRequest(() => accountService.getDocumentsReAgreementStatus(ck)),
    ]);
    final getLoginStatus = responses[0];
    final getDocumentsReAgreementStatus = responses[1];
    if (getLoginStatus.isError) {
      return checkConnectionError<String?>(
        _context,
        getLoginStatus.error,
        onRetry: () => _checkAccountStatus(res),
        isSplash: _isSplash,
      );
    }
    final loginStatus = getLoginStatus.data as LoginStatusModel;
    final documentsReAgreementStatus = getDocumentsReAgreementStatus.data as DocumentsReAgreementStatusModel?;
    if (loginStatus.needsTradingInfoEntry == false) {
      guardRequest(() => accountService.registerLoginHistory(ck)); // no handle error
    }
    String targetUrl = Base.homePageUrl;

    if (loginStatus.needsTradingInfoEntry == true) {
      _turnOffAutoLogin();
      final kcMemberSiteUrl = await getKcMemberSiteUrl(_context, ck.siteid);
      if (kcMemberSiteUrl == null) return null;
      Base.setKcMemberSiteUrl = kcMemberSiteUrl;
      targetUrl = Base.needsTradingInfoEntryUrl;
    } else if (loginStatus.needsFxAccountOpening == true) {
      _turnOffAutoLogin();
      final kcMemberSiteUrl = await getKcMemberSiteUrl(_context, ck.siteid);
      if (kcMemberSiteUrl == null) return null;
      Base.setKcMemberSiteUrl = kcMemberSiteUrl;
      targetUrl = Base.needsFxAccountOpeningUrl;
    } else if (loginStatus.isLoginAllowed == false) {
      _turnOffAutoLogin();
      await DialogUtils.showDl04(_context, _context.l10n.me04);
      return null;
    } else if (loginStatus.hasAuthenticationMailAddress == false) {
      targetUrl = Base.mailRegistrationGuidanceUrl;
    } else if (documentsReAgreementStatus?.needsAgreement == true && documentsReAgreementStatus?.isSkippedAgreement == false) {
      targetUrl = Base.preContractConfirmUrl;
    }
    await syncCookie;
    postDeviceToken(Platform.isAndroid ? fcmToken : apnsToken);
    return targetUrl;
  }

  Future<String?> postLoginProcessing(BuildContext context, LoginModel mfaOauthToken) async {
    setCurrentContext(context);
    _cookiesLite = mfaOauthToken.cookiesLite;
    final targetUrl = await _checkAccountStatus(mfaOauthToken);
    if (targetUrl != null) {
      FirebaseAnalyticsUtils.faAutoLoginTracking(
        username: _username,
        isAutoLogin: _isAutoLogin,
        isSavePassword: _isSavePassword,
        isSaveUsername: _isSaveUsername,
      );
      KarteUtil().identify(secureStorage: secureStorage, userId: _username);
      if (!_isSaveUsername) _username = "";
      if (!_isSavePassword) _password = "";
      secureStorage.saveUsername(_username);
      secureStorage.savePassword(_password);
      sharedPreferences.setBool(StorageKey.isSavePassword, _isSavePassword);
      sharedPreferences.setBool(StorageKey.isSaveUsername, _isSaveUsername);
      sharedPreferences.setBool(StorageKey.isAutoLogin, _isAutoLogin);
      context.replaceRoute(WebViewRoute(initialUrl: targetUrl));
    }
    return targetUrl;
  }

  void _turnOffAutoLogin() {
    sharedPreferences.remove(StorageKey.isAutoLogin);
    toggleAutoLogin(false);
  }

  Future<void> _syncCookies(List<Cookie> cookies) async {
    if (Platform.isAndroid) {
      // DO NOT clear cookies:ref(KCMSR-4672) In Android can duplication but it's still working, (iOS < 12.4 hangs app with clearCookies)
      // await cookieManager.clearCookies();

      // https://pub.dev/packages/webview_cookie_manager#secure-attribute
      final gCookieDomain = groupBy(cookies, (c) => c.domain);
      for (var domain in gCookieDomain.keys) {
        final cks = gCookieDomain[domain] ?? [];
        final gCookieSecure = groupBy(cks, (c) => c.secure);

        for (var isSecure in gCookieSecure.keys) {
          final ckks = gCookieSecure[isSecure]?.toList() ?? [];
          if (ckks.isNotEmpty) {
            debugPrint("_______Cookie: ${ckks.toString()}");
            await cookieManager.setCookies(ckks, origin: isSecure ? 'https://$domain' : null);
          }
        }
      }
    } else {
      await cookieManager.setCookies(cookies);
    }
  }

  /// Keep for mfa
  List<MfaAuthenticatorsModel> _mfaAuthRes = [];
  List<MfaAuthenticatorsModel> get mfaAuth => _mfaAuthRes;
  CookiesModel? _cookiesLite;
  CookiesModel? get cookiesLite => _cookiesLite;

  Future<void> _loginErrorHandle(LoginModel loginRes) async {
    switch (loginRes.error?.errorCode) {
      case ErrorCode.wrongAccount:
      case ErrorCode.wrongAccountKeycloak:
        DialogUtils.showDl04(_context, _context.l10n.me02);
        break;
      case ErrorCode.userBlock:
        DialogUtils.showDl04(_context, _context.l10n.me06);
        break;
      case ErrorCode.mfaRequest:
        final errorRes = loginRes.error!;
        _cookiesLite = loginRes.cookiesLite;
        debugPrint("___mfa_session_id: ${loginRes.cookiesLite?.mfa_session_id}");
        if (errorRes.email.isNotNullOrEmpty && errorRes.email_verified != true) {
          context.replaceRoute(MfaMailVerifyRoute(email: errorRes.email!));
          return;
        }
        final mfaAuthRes = await loginService.authMfaAuthenticators(
          ck: _cookiesLite,
        );
        if (mfaAuthRes.data == null) {
          DialogUtils.showDl04(_context, _context.l10n.me03);
          return;
        }
        _mfaAuthRes = mfaAuthRes.data!;
        final mfaType = loginService.checkPreferredMfaType(_mfaAuthRes);
        if (mfaType.isMfaPush) {
          context.replaceRoute(const MfaPushRoute());
          return;
        }
        if (mfaType.isMfaEmail) {
          context.replaceRoute(const MfaEmailRoute());
          return;
        }
        if (mfaType.isMfaRecovery) {
          context.replaceRoute(const MfaRecoveryInputRoute());
          return;
        }
        DialogUtils.showDl04(_context, _context.l10n.me13);
        break;
      case ErrorCode.auth0Stopped:
        DialogUtils.showDl04(_context, _context.l10n.me09);
        break;
      case ErrorCode.tooManyAttempts:
        DialogUtils.showDl04(_context, _context.l10n.me07);
        break;
      default:
        DialogUtils.showDl04(_context, _context.l10n.me03);
        break;
    }
  }

  Future<String?> getKcMemberSiteUrl(BuildContext context, String? siteId) async {
    switch (siteId) {
      case "0":
        return Base.kcMemberSite0Url;
      case "1":
        return Base.kcMemberSite1Url;
      default:
        await showInvalidSession(context, [
          ActionProps(
              onPressed: (context) async {
                Navigator.pop(context);
                await clearSession();
                context.router.replace(LoginRoute());
              },
              child: Text(context.l10n.close))
        ]);
        return null;
    }
  }

  void getAnnounce() async {
    final announceEffective = await loginService.getAnnounceEffective();
    _maintenanceAnnounce = announceEffective?.content;
    notifyListeners();
  }
}
