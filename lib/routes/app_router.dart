import 'package:auto_route/auto_route.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:kc_member_site_native/constant/base.dart';
import 'package:kc_member_site_native/constant/enum.dart';
import 'package:kc_member_site_native/model/login_model.dart';
import 'package:kc_member_site_native/screen/initial_screen.dart';
import 'package:kc_member_site_native/screen/license_detail_screen.dart';
import 'package:kc_member_site_native/screen/license_screen.dart';
import 'package:kc_member_site_native/screen/login_screen.dart';
import 'package:kc_member_site_native/screen/mfa/index.dart';
import 'package:kc_member_site_native/screen/pdf_viewer_screen.dart';
import 'package:kc_member_site_native/screen/webview_screen.dart';

part 'app_router.gr.dart';

@AutoRouterConfig(
  replaceInRouteName: 'Screen,Route',
)
class AppRouter extends RootStackRouter {
  @override
  RouteType get defaultRouteType => const RouteType.material();

  @override
  final List<AutoRoute> routes = [
    CustomRoute(page: InitialRoute.page, path: '/', transitionsBuilder: TransitionsBuilders.noTransition),
    CustomRoute(page: LoginRoute.page, path: Base.loginPath, transitionsBuilder: TransitionsBuilders.noTransition),
    CustomRoute(page: WebViewRoute.page, path: Base.webviewPath, transitionsBuilder: TransitionsBuilders.noTransition),
    CustomRoute(page: PdfViewerRoute.page, path: Base.pdfViewerPath, transitionsBuilder: TransitionsBuilders.slideBottom),
    AutoRoute(page: LicenseRoute.page, path: Base.licensesPath),
    AutoRoute(page: LicenseDetailRoute.page),
    CustomRoute(page: MfaPushRoute.page, transitionsBuilder: TransitionsBuilders.noTransition),
    CustomRoute(page: MfaSelectRoute.page, transitionsBuilder: TransitionsBuilders.noTransition),
    CustomRoute(page: MfaMailVerifyRoute.page, transitionsBuilder: TransitionsBuilders.noTransition),
    CustomRoute(page: MfaEmailRoute.page, transitionsBuilder: TransitionsBuilders.noTransition),
    CustomRoute(page: MfaRecoveryRoute.page, transitionsBuilder: TransitionsBuilders.noTransition),
    CustomRoute(page: MfaRecoveryInputRoute.page, transitionsBuilder: TransitionsBuilders.noTransition),
    CustomRoute(page: MfaTotpRoute.page, transitionsBuilder: TransitionsBuilders.noTransition),
  ];
}
