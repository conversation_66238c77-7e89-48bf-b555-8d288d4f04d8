import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:kc_member_site_native/constant/base.dart';
import 'package:kc_member_site_native/util/firebase_analytics.dart';

class AppNavigatorObserver extends RouteObserver<ModalRoute<dynamic>> {
  String? _currentPath;
  String? get currentPath => _currentPath;
  set setCurrentPath(String? url) {
    _currentPath = url;
  }

  StackRouter? get router => navigator?.context.router;

  bool get isPdfOpening => currentPath == Base.pdfViewerPath;

  /// Only filter PageRoute because route can is DialogRoute
  bool _routeFilter(Route<dynamic>? route) => route is PageRoute;

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    if (_routeFilter(route)) {
      _currentPath = _getRoutePath(route.settings);
      FirebaseAnalyticsUtils.faScreenTracking(path: _currentPath);
    }
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    if (newRoute != null && _routeFilter(newRoute)) {
      _currentPath = _getRoutePath(newRoute.settings);
      FirebaseAnalyticsUtils.faScreenTracking(path: _currentPath);
    }
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    if (previousRoute != null && _routeFilter(route) && _routeFilter(previousRoute)) {
      _currentPath = _getRoutePath(previousRoute.settings);
      FirebaseAnalyticsUtils.faScreenTracking(path: _currentPath);
    }
  }

  String? _getRoutePath(RouteSettings? settings) {
    final path = (settings is AutoRoutePage ? settings.routeData.path : null);
    debugPrint("____currentPath $path");
    return path;
  }
}
