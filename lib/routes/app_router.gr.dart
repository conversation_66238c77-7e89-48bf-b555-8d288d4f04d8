// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

part of 'app_router.dart';

/// generated route for
/// [InitialScreen]
class InitialRoute extends PageRouteInfo<void> {
  const InitialRoute({List<PageRouteInfo>? children})
      : super(
          InitialRoute.name,
          initialChildren: children,
        );

  static const String name = 'InitialRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const InitialScreen();
    },
  );
}

/// generated route for
/// [LicenseDetailScreen]
class LicenseDetailRoute extends PageRouteInfo<LicenseDetailRouteArgs> {
  LicenseDetailRoute({
    Key? key,
    required String packageName,
    required List<LicenseEntry> licenseEntries,
    required ScrollController? scrollController,
    List<PageRouteInfo>? children,
  }) : super(
          LicenseDetailRoute.name,
          args: LicenseDetailRouteArgs(
            key: key,
            packageName: packageName,
            licenseEntries: licenseEntries,
            scrollController: scrollController,
          ),
          initialChildren: children,
        );

  static const String name = 'LicenseDetailRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<LicenseDetailRouteArgs>();
      return LicenseDetailScreen(
        key: args.key,
        packageName: args.packageName,
        licenseEntries: args.licenseEntries,
        scrollController: args.scrollController,
      );
    },
  );
}

class LicenseDetailRouteArgs {
  const LicenseDetailRouteArgs({
    this.key,
    required this.packageName,
    required this.licenseEntries,
    required this.scrollController,
  });

  final Key? key;

  final String packageName;

  final List<LicenseEntry> licenseEntries;

  final ScrollController? scrollController;

  @override
  String toString() {
    return 'LicenseDetailRouteArgs{key: $key, packageName: $packageName, licenseEntries: $licenseEntries, scrollController: $scrollController}';
  }
}

/// generated route for
/// [LicenseScreen]
class LicenseRoute extends PageRouteInfo<LicenseRouteArgs> {
  LicenseRoute({
    Key? key,
    String? applicationName,
    String? applicationVersion,
    Widget? applicationIcon,
    String? applicationLegalese,
    List<PageRouteInfo>? children,
  }) : super(
          LicenseRoute.name,
          args: LicenseRouteArgs(
            key: key,
            applicationName: applicationName,
            applicationVersion: applicationVersion,
            applicationIcon: applicationIcon,
            applicationLegalese: applicationLegalese,
          ),
          initialChildren: children,
        );

  static const String name = 'LicenseRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<LicenseRouteArgs>(orElse: () => const LicenseRouteArgs());
      return LicenseScreen(
        key: args.key,
        applicationName: args.applicationName,
        applicationVersion: args.applicationVersion,
        applicationIcon: args.applicationIcon,
        applicationLegalese: args.applicationLegalese,
      );
    },
  );
}

class LicenseRouteArgs {
  const LicenseRouteArgs({
    this.key,
    this.applicationName,
    this.applicationVersion,
    this.applicationIcon,
    this.applicationLegalese,
  });

  final Key? key;

  final String? applicationName;

  final String? applicationVersion;

  final Widget? applicationIcon;

  final String? applicationLegalese;

  @override
  String toString() {
    return 'LicenseRouteArgs{key: $key, applicationName: $applicationName, applicationVersion: $applicationVersion, applicationIcon: $applicationIcon, applicationLegalese: $applicationLegalese}';
  }
}

/// generated route for
/// [LoginScreen]
class LoginRoute extends PageRouteInfo<LoginRouteArgs> {
  LoginRoute({
    Key? key,
    ActionType? actionType,
    List<PageRouteInfo>? children,
  }) : super(
          LoginRoute.name,
          args: LoginRouteArgs(
            key: key,
            actionType: actionType,
          ),
          initialChildren: children,
        );

  static const String name = 'LoginRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<LoginRouteArgs>(orElse: () => const LoginRouteArgs());
      return LoginScreen(
        key: args.key,
        actionType: args.actionType,
      );
    },
  );
}

class LoginRouteArgs {
  const LoginRouteArgs({
    this.key,
    this.actionType,
  });

  final Key? key;

  final ActionType? actionType;

  @override
  String toString() {
    return 'LoginRouteArgs{key: $key, actionType: $actionType}';
  }
}

/// generated route for
/// [MfaEmailScreen]
class MfaEmailRoute extends PageRouteInfo<void> {
  const MfaEmailRoute({List<PageRouteInfo>? children})
      : super(
          MfaEmailRoute.name,
          initialChildren: children,
        );

  static const String name = 'MfaEmailRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const MfaEmailScreen());
    },
  );
}

/// generated route for
/// [MfaMailVerifyScreen]
class MfaMailVerifyRoute extends PageRouteInfo<MfaMailVerifyRouteArgs> {
  MfaMailVerifyRoute({
    Key? key,
    required String email,
    List<PageRouteInfo>? children,
  }) : super(
          MfaMailVerifyRoute.name,
          args: MfaMailVerifyRouteArgs(
            key: key,
            email: email,
          ),
          initialChildren: children,
        );

  static const String name = 'MfaMailVerifyRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<MfaMailVerifyRouteArgs>();
      return WrappedRoute(
          child: MfaMailVerifyScreen(
        key: args.key,
        email: args.email,
      ));
    },
  );
}

class MfaMailVerifyRouteArgs {
  const MfaMailVerifyRouteArgs({
    this.key,
    required this.email,
  });

  final Key? key;

  final String email;

  @override
  String toString() {
    return 'MfaMailVerifyRouteArgs{key: $key, email: $email}';
  }
}

/// generated route for
/// [MfaPushScreen]
class MfaPushRoute extends PageRouteInfo<void> {
  const MfaPushRoute({List<PageRouteInfo>? children})
      : super(
          MfaPushRoute.name,
          initialChildren: children,
        );

  static const String name = 'MfaPushRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const MfaPushScreen());
    },
  );
}

/// generated route for
/// [MfaRecoveryInputScreen]
class MfaRecoveryInputRoute extends PageRouteInfo<void> {
  const MfaRecoveryInputRoute({List<PageRouteInfo>? children})
      : super(
          MfaRecoveryInputRoute.name,
          initialChildren: children,
        );

  static const String name = 'MfaRecoveryInputRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const MfaRecoveryInputScreen());
    },
  );
}

/// generated route for
/// [MfaRecoveryScreen]
class MfaRecoveryRoute extends PageRouteInfo<MfaRecoveryRouteArgs> {
  MfaRecoveryRoute({
    Key? key,
    required String newRecoveryCode,
    required LoginModel mfaOauthToken,
    List<PageRouteInfo>? children,
  }) : super(
          MfaRecoveryRoute.name,
          args: MfaRecoveryRouteArgs(
            key: key,
            newRecoveryCode: newRecoveryCode,
            mfaOauthToken: mfaOauthToken,
          ),
          initialChildren: children,
        );

  static const String name = 'MfaRecoveryRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<MfaRecoveryRouteArgs>();
      return MfaRecoveryScreen(
        key: args.key,
        newRecoveryCode: args.newRecoveryCode,
        mfaOauthToken: args.mfaOauthToken,
      );
    },
  );
}

class MfaRecoveryRouteArgs {
  const MfaRecoveryRouteArgs({
    this.key,
    required this.newRecoveryCode,
    required this.mfaOauthToken,
  });

  final Key? key;

  final String newRecoveryCode;

  final LoginModel mfaOauthToken;

  @override
  String toString() {
    return 'MfaRecoveryRouteArgs{key: $key, newRecoveryCode: $newRecoveryCode, mfaOauthToken: $mfaOauthToken}';
  }
}

/// generated route for
/// [MfaSelectScreen]
class MfaSelectRoute extends PageRouteInfo<void> {
  const MfaSelectRoute({List<PageRouteInfo>? children})
      : super(
          MfaSelectRoute.name,
          initialChildren: children,
        );

  static const String name = 'MfaSelectRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const MfaSelectScreen());
    },
  );
}

/// generated route for
/// [MfaTotpScreen]
class MfaTotpRoute extends PageRouteInfo<void> {
  const MfaTotpRoute({List<PageRouteInfo>? children})
      : super(
          MfaTotpRoute.name,
          initialChildren: children,
        );

  static const String name = 'MfaTotpRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const MfaTotpScreen());
    },
  );
}

/// generated route for
/// [PdfViewerScreen]
class PdfViewerRoute extends PageRouteInfo<PdfViewerRouteArgs> {
  PdfViewerRoute({
    required String pdfUrl,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          PdfViewerRoute.name,
          args: PdfViewerRouteArgs(
            pdfUrl: pdfUrl,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'PdfViewerRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<PdfViewerRouteArgs>();
      return PdfViewerScreen(
        args.pdfUrl,
        key: args.key,
      );
    },
  );
}

class PdfViewerRouteArgs {
  const PdfViewerRouteArgs({
    required this.pdfUrl,
    this.key,
  });

  final String pdfUrl;

  final Key? key;

  @override
  String toString() {
    return 'PdfViewerRouteArgs{pdfUrl: $pdfUrl, key: $key}';
  }
}

/// generated route for
/// [WebViewScreen]
class WebViewRoute extends PageRouteInfo<WebViewRouteArgs> {
  WebViewRoute({
    required String initialUrl,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          WebViewRoute.name,
          args: WebViewRouteArgs(
            initialUrl: initialUrl,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'WebViewRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<WebViewRouteArgs>();
      return WrappedRoute(
          child: WebViewScreen(
        args.initialUrl,
        key: args.key,
      ));
    },
  );
}

class WebViewRouteArgs {
  const WebViewRouteArgs({
    required this.initialUrl,
    this.key,
  });

  final String initialUrl;

  final Key? key;

  @override
  String toString() {
    return 'WebViewRouteArgs{initialUrl: $initialUrl, key: $key}';
  }
}
