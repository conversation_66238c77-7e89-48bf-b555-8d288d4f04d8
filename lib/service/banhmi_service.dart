import 'package:flutter/material.dart';
import 'package:kc_member_site_native/constant/base.dart';
import 'package:kc_member_site_native/model/geo_location_model.dart';
import 'package:kc_member_site_native/repository/client/kcm_client.dart';
import 'package:kc_member_site_native/repository/geo_repository.dart';

abstract class BanhmiService {
  Future<GeoLocationData?> getGeoLocation();
}

class BanhmiServiceImpl extends BanhmiService {
  late GeoRepository _geoRepository;

  BanhmiServiceImpl() {
    _geoRepository = GeoRepository.create(KcmApiClientCreator().create(
      baseUrl: Base.banhmiDomainUrl,
      timeout: Base.getGeoTimeout,
    ));
  }

  @override
  Future<GeoLocationData?> getGeoLocation() async {
    try {
      final response = await _geoRepository.getGeoLocation(
        channel: "3", // fixed
        geoIP2CheckToken: Base.geoIP2CheckToken,
      );
      if (response.isSuccessful) {
        return GeoLocationModel.fromJson(response.body as Map<String, dynamic>).data;
      }
    } catch (e) {
      debugPrint('getGeoLocation fail: ${e.toString()}');
    }
    return null;
  }
}
