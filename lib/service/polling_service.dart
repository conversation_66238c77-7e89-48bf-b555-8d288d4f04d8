import 'dart:async';

import 'package:async/async.dart';

abstract class PollingService {
  Future<void> polling<T>(
    Future<T?> Function() request, {
    required void Function(T?) onResult,
    required int seconds,
  });
  Future<void> cancel();
}

class PollingServiceImpl extends PollingService {
  CancelableOperation<dynamic>? _cancellableOperation;
  CancelableOperation<void>? _nextFetchOperation;

  Future<T> _cancellable<T>(Future<T> Function() future) async {
    _cancellableOperation = CancelableOperation<T>.fromFuture(
      future(),
    );
    return (await _cancellableOperation!.value) as T;
  }

  @override
  Future<void> polling<T>(
    Future<T?> Function() request, {
    required void Function(T?) onResult,
    required int seconds,
  }) async {
    await cancel();
    _nextFetchOperation = CancelableOperation.fromFuture(Future<void>.delayed(Duration(seconds: seconds)));

    final result = await _cancellable(() => request());
    if (_cancellableOperation == null || _cancellableOperation!.isCanceled) {
      return;
    }
    onResult.call(result);

    await _nextFetchOperation?.value;

    if (_nextFetchOperation == null || _nextFetchOperation!.isCanceled) {
      return;
    }

    await polling(request, onResult: onResult, seconds: seconds);
  }

  @override
  Future<void> cancel() async {
    await _nextFetchOperation?.cancel();
    await _cancellableOperation?.cancel();
    _nextFetchOperation = null;
    _cancellableOperation = null;
  }
}
