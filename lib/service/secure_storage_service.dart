import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:kc_member_site_native/constant/storage.dart';

abstract class SecureStorageService {
  Future<void> saveUsername(String username);
  Future<void> savePassword(String password);
  Future<String?> getUsername();
  Future<String?> getPassword();
  Future<void> deleteAll();

  Future<String?> getAuthenticatorId();
  Future<void> setAuthenticatorId(String? id);

  Future<Map<String, bool>> getUserIdKarte();
  Future<void> saveUserIdKarte(Map<String, bool> usersMap);
  Future<void> delete(String key);

  Future<void> saveAppReviewHistory(Map<String, List<String>> history);
  Future<Map<String, List<String>>> getAppReviewHistory();
}

class SecureStorageServiceImpl implements SecureStorageService {
  late FlutterSecureStorage _secureStorage;

  SecureStorageServiceImpl() {
    _secureStorage = const FlutterSecureStorage();
  }

  @override
  Future<String?> getPassword() {
    return _secureStorage.read(key: StorageKey.password);
  }

  @override
  Future<String?> getUsername() {
    return _secureStorage.read(key: StorageKey.username);
  }

  @override
  Future<void> deleteAll() {
    return _secureStorage.deleteAll();
  }

  @override
  Future<void> delete(String key) {
    return _secureStorage.delete(key: key);
  }

  @override
  Future<void> savePassword(String password) {
    return _secureStorage.write(key: StorageKey.password, value: password);
  }

  @override
  Future<void> saveUsername(String username) {
    return _secureStorage.write(key: StorageKey.username, value: username);
  }

  @override
  Future<String?> getAuthenticatorId() {
    return _secureStorage.read(key: StorageKey.authenticatorId);
  }

  @override
  Future<void> setAuthenticatorId(String? id) {
    return _secureStorage.write(key: StorageKey.authenticatorId, value: id);
  }

  @override
  Future<Map<String, bool>> getUserIdKarte() async {
    final stringValue = await _secureStorage.read(key: StorageKey.userIdKarte);
    final usersMap = jsonDecode(stringValue ?? "{}") as Map<String, dynamic>;
    return usersMap.map((key, value) => MapEntry(key, value));
  }

  @override
  Future<void> saveUserIdKarte(Map<String, bool> usersMap) async {
    return _secureStorage.write(key: StorageKey.userIdKarte, value: jsonEncode(usersMap));
  }

  @override
  Future<void> saveAppReviewHistory(Map<String, List<String>> history) {
    return _secureStorage.write(key: StorageKey.appReviewHistory, value: jsonEncode(history));
  }

  @override
  Future<Map<String, List<String>>> getAppReviewHistory() async {
    final dataString = await _secureStorage.read(key: StorageKey.appReviewHistory);
    try {
      final data = jsonDecode(dataString ?? "{}") as Map<String, dynamic>;
      return data.map((key, value) => MapEntry(key, List<String>.from(value)));
    } catch (e) {
      return {};
    }
  }
}
