import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:kc_member_site_native/constant/base.dart';
import 'package:kc_member_site_native/constant/enum.dart';
import 'package:kc_member_site_native/model/annnounce_model.dart';
import 'package:kc_member_site_native/model/base_response.dart';
import 'package:kc_member_site_native/model/login_model.dart';
import 'package:kc_member_site_native/model/login_status_model.dart';
import 'package:kc_member_site_native/model/mfa_error_model.dart';
import 'package:kc_member_site_native/model/mfa_model.dart';
import 'package:kc_member_site_native/repository/auth_repository.dart';
import 'package:kc_member_site_native/repository/login_repository.dart';

import '../main.dart';

abstract class LoginService {
  Future<LoginModel> spNativeLoginRop({
    required String username,
    required String password,
    String? appCheckToken,
  });

  Future<BaseResponse<bool>> authCodeRegistration({
    required String nativeState,
    required String code,
    required CookiesModel? ck,
  });

  Future<BaseResponse<List<MfaAuthenticatorsModel>?>> authMfaAuthenticators({
    required CookiesModel? ck,
  });
  Future<MfaChallengeModel> authMfaChallenge({
    required CookiesModel? ck,
    required String? authenticatorId,
    required ChallengeType challengeType,
  });
  Future<LoginModel> authMfaOauthToken({
    required CookiesModel? ck,
    required GrantType grantType,
    String? oobCode,
    String? otp,
    String? bindingCode,
    String? recoveryCode,
  });
  Future<BaseResponse<MfaErrorModel?>> authMfaEmailResend({
    required CookiesModel? ck,
  });
  Future<AnnnounceEffectiveModel?> getAnnounceEffective();
  Future<LoginStatusModel?> getServiceAvailable();

  MfaTypeModel checkPreferredMfaType(List<MfaAuthenticatorsModel> mfaAuthRes);
}

class LoginServiceImpl extends LoginService {
  late LoginRepository _loginRepository;
  late AuthRepository _authRepository;

  LoginServiceImpl() {
    _loginRepository = LoginRepository.create(bffApiClient);
    _authRepository = AuthRepository.create(kcmApiClient);
  }

  @override
  Future<LoginModel> spNativeLoginRop({
    required String username,
    required String password,
    String? appCheckToken,
  }) async {
    int? statusCode;
    try {
      final response = await _loginRepository.spNativeLoginRop(
        username: username,
        password: password,
        appCheckToken: appCheckToken,
      );
      final cookiesString = _getSetCookie(response.headers);
      final loginRes = LoginModel.fromCookiesString(cookiesString);
      if (response.isSuccessful) {
        return loginRes;
      }
      statusCode = response.statusCode;
      return loginRes.copyWith(
        error: LoginErrorModel.fromJson(response.error, response.statusCode),
      );
    } catch (e) {
      debugPrint('spNativeLoginRop fail: ${e.toString()}');
      return LoginModel(
        error: LoginErrorModel(
          error_description: e.toString(),
          httpStatus: statusCode,
        ),
      );
    }
  }

  @override
  Future<BaseResponse<bool>> authCodeRegistration({
    required String nativeState,
    required String code,
    required CookiesModel? ck,
  }) async {
    try {
      final response = await _authRepository.authCodeRegistration(
        nativeState: nativeState,
        code: code,
        xt: ck?.xt,
        cookie: "se=${ck?.se}",
      );
      return BaseResponse(statusCode: response.statusCode);
    } catch (e) {
      debugPrint('authCodeRegistration fail: ${e.toString()}');
      return const BaseResponse();
    }
  }

  String _getSetCookie(final Map<String, dynamic> headers) {
    for (final key in headers.keys) {
      // システムによって返却される "set-cookie" のケースはバラバラです。
      if (key.toLowerCase() == 'set-cookie') {
        return headers[key] as String;
      }
    }

    return '';
  }

  @override
  Future<BaseResponse<List<MfaAuthenticatorsModel>?>> authMfaAuthenticators({
    required CookiesModel? ck,
  }) async {
    try {
      final response = await _authRepository.mfaAuthenticators(
        xt: ck?.xt,
        cookie: "mfa_session_id=${ck?.mfa_session_id}",
      );
      if (response.statusCode == 200) {
        final body = response.body as List<dynamic>?;
        return BaseResponse(
          statusCode: response.statusCode,
          data: body?.map((e) => MfaAuthenticatorsModel.fromJson(e)).toList(),
        );
      }
      return BaseResponse(statusCode: response.statusCode);
    } catch (e) {
      debugPrint('authMfaAuthenticators fail: ${e.toString()}');
    }
    return const BaseResponse();
  }

  @override
  Future<MfaChallengeModel> authMfaChallenge({
    required CookiesModel? ck,
    required String? authenticatorId,
    required ChallengeType challengeType,
  }) async {
    int? statusCode;
    try {
      final response = await _authRepository.authMfaChallenge(
        challengeType: challengeType.value,
        authenticatorId: authenticatorId,
        xt: ck?.xt,
        cookie: "mfa_session_id=${ck?.mfa_session_id}",
      );
      if (response.isSuccessful) {
        return MfaChallengeModel.fromJson(response.body);
      }
      statusCode = response.statusCode;
      return MfaChallengeModel(error: LoginErrorModel.fromJson(response.error, response.statusCode));
    } catch (e) {
      debugPrint('authMfaChallenge fail: ${e.toString()}');
      return MfaChallengeModel(
        error: LoginErrorModel(
          error_description: e.toString(),
          httpStatus: statusCode,
        ),
      );
    }
  }

  @override
  Future<LoginModel> authMfaOauthToken({
    required CookiesModel? ck,
    required GrantType grantType,
    String? oobCode,
    String? otp,
    String? bindingCode,
    String? recoveryCode,
  }) async {
    int? statusCode;
    try {
      final response = await _loginRepository.authMfaOauthToken(
        xt: ck?.xt,
        cookie: "mfa_session_id=${ck?.mfa_session_id}",
        grantType: grantType.value,
        oobCode: oobCode,
        otp: otp,
        bindingCode: bindingCode,
        recoveryCode: recoveryCode,
      );
      if (response.isSuccessful) {
        final cookiesString = _getSetCookie(response.headers);
        final bodyRes = LoginModel.fromJson(jsonDecode(response.body == '' ? '{}' : response.body));
        return LoginModel.fromCookiesString(cookiesString).copyWith(
          recovery_code: bodyRes.recovery_code,
        );
      }
      statusCode = response.statusCode;
      return LoginModel(error: LoginErrorModel.fromJson(response.error, response.statusCode));
    } catch (e) {
      debugPrint('authMfaOauthToken fail: ${e.toString()}');
      return LoginModel(
        error: LoginErrorModel(
          error_description: e.toString(),
          httpStatus: statusCode,
        ),
      );
    }
  }

  @override
  Future<BaseResponse<MfaErrorModel?>> authMfaEmailResend({required CookiesModel? ck}) async {
    int? statusCode;
    try {
      final response = await _authRepository.authMfaEmailResend(
        xt: ck?.xt,
        cookie: "mfa_session_id=${ck?.mfa_session_id}",
      );
      if (response.isSuccessful) {
        return const BaseResponse(statusCode: 200);
      }
      statusCode = response.statusCode;
      return BaseResponse(
        statusCode: statusCode,
        data: MfaErrorModel.fromJson(response.error as Map<String, dynamic>),
      );
    } catch (e) {
      debugPrint('authMfaEmailResend fail: ${e.toString()}');
    }
    return BaseResponse(
      statusCode: statusCode,
    );
  }

  @override
  Future<AnnnounceEffectiveModel?> getAnnounceEffective() async {
    try {
      final response = await kcmApiClient.get(
        Uri.parse('/api/v1/announcements/effective'),
        baseUrl: Uri.parse(Base.announceDomainUrl),
      );
      if (response.isSuccessful) {
        return AnnnounceEffectiveModel.fromJson(response.body as Map<String, dynamic>);
      }
    } catch (e) {
      debugPrint('getAnnounceEffective fail: ${e.toString()}');
    }
    return null;
  }

  @override
  Future<LoginStatusModel?> getServiceAvailable() async {
    try {
      final response = await kcmApiClient.get(
        Uri.parse('/api/v1/isServiceAvailable'),
        baseUrl: Uri.parse(Base.loginStatusDomainUrl),
      );
      if (response.isSuccessful) {
        return LoginStatusModel.fromJson(response.body as Map<String, dynamic>);
      }
    } catch (e) {
      debugPrint('getServiceAvailable fail: ${e.toString()}');
    }
    return null;
  }

  @override
  MfaTypeModel checkPreferredMfaType(List<MfaAuthenticatorsModel> mfaAuthRes) {
    MfaTypeModel res = const MfaTypeModel();
    for (final mfaAuth in mfaAuthRes) {
      if (mfaAuth.isMfaPush) {
        res = res.copyWith(isMfaPush: true);
      } else if (mfaAuth.isMfaEmail) {
        res = res.copyWith(isMfaEmail: true);
      } else if (mfaAuth.isMfaRecovery) {
        res = res.copyWith(isMfaRecovery: true);
      }
    }
    return res;
  }
}
