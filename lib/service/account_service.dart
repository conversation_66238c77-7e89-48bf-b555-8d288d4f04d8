import 'package:flutter/material.dart';
import 'package:kc_member_site_native/model/account_model.dart';
import 'package:kc_member_site_native/model/login_model.dart';
import 'package:kc_member_site_native/repository/account_repository.dart';

import '../main.dart';

abstract class AccountService {
  Future<LoginStatusModel?> getLoginStatus(CookiesModel ck);

  Future<bool> registerLoginHistory(CookiesModel ck);

  Future<DocumentsReAgreementStatusModel?> getDocumentsReAgreementStatus(CookiesModel ck);
}

class AccountServiceImpl extends AccountService {
  late AccountRepository _accountRepository;
  AccountServiceImpl() {
    _accountRepository = AccountRepository.create(kcmApiClient);
  }

  @override
  Future<DocumentsReAgreementStatusModel?> getDocumentsReAgreementStatus(CookiesModel ck) async {
    try {
      final response = await _accountRepository.getDocumentsReAgreementStatus(ck.xt, "se=${ck.se}");
      if (response.isSuccessful) {
        return DocumentsReAgreementStatusModel.fromJson(response.body as Map<String, dynamic>);
      }
      throw response;
    } catch (e) {
      debugPrint('getDocumentsReAgreementStatus fail: ${e.toString()}');
      rethrow;
    }
  }

  @override
  Future<LoginStatusModel?> getLoginStatus(CookiesModel ck) async {
    try {
      final response = await _accountRepository.getLoginStatus(ck.xt, "se=${ck.se}");
      if (response.isSuccessful) {
        return LoginStatusModel.fromJson(response.body as Map<String, dynamic>);
      }
      throw response;
    } catch (e) {
      debugPrint('getLoginStatus fail: ${e.toString()}');
      rethrow;
    }
  }

  @override
  Future<bool> registerLoginHistory(CookiesModel ck) async {
    try {
      final response = await _accountRepository.registerLoginHistory(ck.xt, "se=${ck.se}");
      return response.isSuccessful;
    } catch (e) {
      debugPrint('registerLoginHistory fail: ${e.toString()}');
      return false;
    }
  }
}
