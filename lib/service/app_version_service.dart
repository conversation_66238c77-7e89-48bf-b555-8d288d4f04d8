import 'package:flutter/material.dart';
import 'package:kc_member_site_native/util/app_info.dart';

import '../main.dart';
import '../model/check_version_model.dart';
import '../repository/app_version_repository.dart';
import '../util/platform_type.dart';

abstract class AppVersionService {
  Future<CheckVersionModel?> checkVersion();
}

class AppVersionServiceImpl extends AppVersionService {
  AppVersionServiceImpl() {
    _appVersionRepository = AppVersionRepository.create(kcmApiClient);
  }
  late AppVersionRepository _appVersionRepository;

  @override
  Future<CheckVersionModel?> checkVersion() async {
    try {
      final version = AppInfo().packageInfo.version.split('.');
      final response = await _appVersionRepository.checkVersion(
          platformType: PlatformType.getPlatformType,
          majorVersion: int.parse(version.first),
          minorVersion: int.parse(version[1]),
          buildVersion: int.parse(version.last));
      if (response.isSuccessful) {
        return CheckVersionModel.fromJson(response.body as Map<String, dynamic>);
      } else {
        throw response;
      }
    } catch (e) {
      debugPrint('checkVersion fail: ${e.toString()}');
      rethrow;
    }
  }
}
