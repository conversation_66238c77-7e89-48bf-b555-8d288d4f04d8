import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/gen/fonts.gen.dart';
import 'package:kc_member_site_native/main.dart';
import 'package:kc_member_site_native/model/karte_attribute_model.dart';
import 'package:kc_member_site_native/routes/app_navigator_observer.dart';
import 'package:kc_member_site_native/routes/app_router.dart';
import 'package:kc_member_site_native/themes/app_colors.dart';
import 'package:kc_member_site_native/util/firebase_analytics.dart';
import 'package:kc_member_site_native/util/karte_util.dart';
import 'package:kc_member_site_native/widgets/single_touch_container.dart';

import 'model/timeline_payload_model.dart';
import 'util/make_local_notification.dart';
import 'util/run_javascript.dart';

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  static AppNavigatorObserver observer = AppNavigatorObserver();

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  static Map<int, Color> swatch = {
    50: const Color(0xffffebe0),
    100: const Color(0xffffccb3),
    200: const Color(0xffffab80),
    300: const Color(0xffff894d),
    400: const Color(0xffff6f26),
    500: const Color(0xffff5600),
    600: const Color(0xffff4f00),
    700: const Color(0xffff4500),
    800: const Color(0xffff3c00),
    900: const Color(0xffff2b00),
  };
  final _appRouter = AppRouter();

  @override
  Widget build(BuildContext context) {
    final mq = MediaQueryData.fromView(View.of(context));
    return ScreenUtilInit(
      designSize: Size(375, 812 + mq.padding.top + mq.padding.bottom / 2),
      minTextAdapt: true,
      builder: (BuildContext context, Widget? child) {
        return MaterialApp.router(
          locale: const Locale('ja', 'JP'),
          localizationsDelegates: AppLocalizations.localizationsDelegates,
          supportedLocales: AppLocalizations.supportedLocales,
          title: '',
          theme: ThemeData(
            useMaterial3: false,
            primarySwatch: MaterialColor(0xffff5600, swatch),
            fontFamily: FontFamily.notoSansJP,
            appBarTheme: const AppBarTheme(
              systemOverlayStyle: SystemUiOverlayStyle(
                statusBarColor: Colors.transparent,
                statusBarBrightness: Brightness.light, // for iOS
                statusBarIconBrightness: Brightness.dark, // for Android
              ),
            ),
            scaffoldBackgroundColor: AppColors.mainBgColor,
            brightness: Brightness.light,
            splashColor: AppColors.orange100Color,
            highlightColor: AppColors.orange200Color,
          ),
          routerDelegate: AutoRouterDelegate(
            _appRouter,
            navigatorObservers: () => [MyApp.observer], // AutoRouterDelegate.defaultNavigatorObserversBuilder,
          ),
          routeInformationParser: _appRouter.defaultRouteParser(),
          builder: (context, child) {
            return MediaQuery(
              // Setting font does not change with system font size
              data: MediaQuery.of(context).copyWith(textScaler: const TextScaler.linear(1.0), boldText: false),
              child: SingleTouchContainer(
                child: child ?? const SizedBox.shrink(),
              ),
            );
          },
        );
      },
    );
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    // Set status bar transparent
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarBrightness: Brightness.light,
      statusBarIconBrightness: Brightness.dark,
    ));

    _initFirebaseMessaging();

    super.initState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      FirebaseAnalyticsUtils.faScreenTracking(path: MyApp.observer.currentPath);
    }
  }

  void _initFirebaseMessaging() {
    FirebaseMessaging.onMessage.listen((RemoteMessage remoteMessage) async {
      if (await KarteUtil().isKartePush(remoteMessage)) {
        await makeKarteLocalNotification(remoteMessage.data);
        return;
      }
      if (Platform.isIOS) return; // iOS handled by apns
      await makeLocalNotification(remoteMessage.notification!, remoteMessage.data);
    });

    Future.delayed(Duration.zero, () async {
      try {
        final token = await FirebaseMessaging.instance.getToken() ?? '';
        if (token.isNotEmpty) {
          if (Platform.isAndroid) {
            postDeviceToken(token);
          }
          fcmToken = token;
          debugPrint('token: $fcmToken');
          KarteUtil().registerFCMToken();
        }
      } catch (e) {
        debugPrint('error token: ${e.toString()}');
      }
    });

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) async {
      _doPostNotifyData(message);
    });

    FirebaseMessaging.instance.getInitialMessage().then((RemoteMessage? message) {
      debugPrint('A new getInitialMessage() event was published!');
      if (message != null) {
        debugPrint('Received FCM message from: ${message.from} with data: ${message.data}');
        _doPostNotifyData(message);
      }
    });

    FirebaseMessaging.instance.onTokenRefresh.listen((token) {
      fcmToken = token;
      if (Platform.isAndroid) {
        postDeviceToken(token);
      }
      debugPrint('New Token: $fcmToken');
      KarteUtil().registerFCMToken();
    });
  }

  void _doPostNotifyData(RemoteMessage message) async {
    if (await KarteUtil().isKartePush(message)) {
      final data = KarteAttributeModel.fromJson(message.data);
      KarteUtil().postNotifyKarteData(data.targetUrl);
      return;
    }
    if (Platform.isIOS) return; // iOS handled by apns
    debugPrint('fcm payload: ${message.toString()}');
    final timelineId = message.data['TimeLineID'];
    final customsortDivision = message.data['CustomsortDivision'];
    final remapData = {
      'TimeLineID': timelineId is String ? json.decode(timelineId) : timelineId,
      'CustomsortDivision': customsortDivision is String ? json.decode(customsortDivision) : customsortDivision
    };
    try {
      TimelinePayloadModel timelinePayloadModel = TimelinePayloadModel.fromJson(remapData);
      postNotifyData(timelinePayloadModel);
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}
