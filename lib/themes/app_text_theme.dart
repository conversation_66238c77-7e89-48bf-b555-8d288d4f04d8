import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/gen/fonts.gen.dart';
import 'package:kc_member_site_native/themes/app_colors.dart';

class AppTextTheme {
  const AppTextTheme._();

  // Base
  static const textStyle = TextStyle(color: AppColors.textColor);
  static const textNumberStyle = TextStyle(fontFamily: FontFamily.openSans, fontWeight: FontWeight.w600, color: AppColors.textColor);
  static const textLinkStyle = TextStyle(
    color: AppColors.primaryColor,
    decoration: TextDecoration.underline,
    fontWeight: FontWeight.bold,
  );

  static final text500Style = textStyle.copyWith(fontSize: 14.w, fontWeight: FontWeight.w500);

  // Text height
  static const height18_12 = 18 / 12;
  static const height18_14 = 18 / 14;
  static const height24_16 = 24 / 16;
}
