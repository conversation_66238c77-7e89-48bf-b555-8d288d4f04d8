import 'package:flutter/material.dart';

class AppColors {
  AppColors._();
  static const Color mainBgColor = Color(0xfff4f4f4);

  static const Color textColor = Color(0xff484848);
  static const Color primaryColor = Color(0xffff5600);
  static const Color orange100Color = Color(0xffFFECE2);
  static const Color orange200Color = Color.fromRGBO(255, 86, 0, 0.2);
  static const Color borderColor = Color(0xffd4d4d4);
  static const Color pinCodeBgColor = Color.fromRGBO(237, 237, 237, 0.6);
  static const Color gray500Color = Color(0xff2C2D30);
  static const Color gray200Color = Color(0xff707070);
  static const Color gray100Color = Color(0xff89898B);
  static const Color gray10Color = Color(0xffEDEDED);

  static const Color dropShadowColor = Color.fromRGBO(165, 165, 165, 0.6);
  static const Color dropShadowToast = Color.fromRGBO(165, 165, 165, 0.3);
  static const Color disableBgColor = Color(0xffCCCCCC);
  static const Color dialogBg = Color.fromRGBO(0, 0, 0, 0.3);
}
