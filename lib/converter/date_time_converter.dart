import 'package:json_annotation/json_annotation.dart';
import 'package:kc_member_site_native/constant/date_time.dart';
import 'package:kc_member_site_native/extensions/date_extensions.dart';
import 'package:kc_member_site_native/extensions/string_extensions.dart';

class DateTimeConverter implements JsonConverter<DateTime?, String?> {
  const DateTimeConverter();

  @override
  DateTime? fromJson(String? json) {
    return json.toDateTime();
  }

  @override
  String? toJson(DateTime? object) {
    return object.toStringFormat(DateTimeConst.yyyyMMddHHmmss);
  }
}
