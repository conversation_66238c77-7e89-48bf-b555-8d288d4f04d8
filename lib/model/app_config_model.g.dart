// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppConfigModel _$AppConfigModelFromJson(Map<String, dynamic> json) =>
    AppConfigModel(
      app_config: AppConfigContentModel.fromJson(
          json['app_config'] as Map<String, dynamic>),
      appReview: json['appReview'] == null
          ? null
          : AppReviewModel.fromJson(json['appReview'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AppConfigModelToJson(AppConfigModel instance) =>
    <String, dynamic>{
      'app_config': instance.app_config,
      'appReview': instance.appReview,
    };

AppConfigContentModel _$AppConfigContentModelFromJson(
        Map<String, dynamic> json) =>
    AppConfigContentModel(
      wl: AppConfigDomainWiModel.fromJson(json['wl'] as Map<String, dynamic>),
      openwithbrowser: AppConfigDomainOpenwithbrowserModel.fromJson(
          json['openwithbrowser'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AppConfigContentModelToJson(
        AppConfigContentModel instance) =>
    <String, dynamic>{
      'wl': instance.wl,
      'openwithbrowser': instance.openwithbrowser,
    };

AppConfigDomainWiModel _$AppConfigDomainWiModelFromJson(
        Map<String, dynamic> json) =>
    AppConfigDomainWiModel(
      domain:
          (json['domain'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$AppConfigDomainWiModelToJson(
        AppConfigDomainWiModel instance) =>
    <String, dynamic>{
      'domain': instance.domain,
    };

AppConfigDomainOpenwithbrowserModel
    _$AppConfigDomainOpenwithbrowserModelFromJson(Map<String, dynamic> json) =>
        AppConfigDomainOpenwithbrowserModel(
          urlincludes: (json['urlincludes'] as List<dynamic>)
              .map((e) => e as String)
              .toList(),
        );

Map<String, dynamic> _$AppConfigDomainOpenwithbrowserModelToJson(
        AppConfigDomainOpenwithbrowserModel instance) =>
    <String, dynamic>{
      'urlincludes': instance.urlincludes,
    };

AppReviewModel _$AppReviewModelFromJson(Map<String, dynamic> json) =>
    AppReviewModel(
      startDate:
          const DateTimeConverter().fromJson(json['startDate'] as String?),
      endDate: const DateTimeConverter().fromJson(json['endDate'] as String?),
    );

Map<String, dynamic> _$AppReviewModelToJson(AppReviewModel instance) =>
    <String, dynamic>{
      'startDate': const DateTimeConverter().toJson(instance.startDate),
      'endDate': const DateTimeConverter().toJson(instance.endDate),
    };
