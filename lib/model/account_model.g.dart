// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginStatusModel _$LoginStatusModelFromJson(Map<String, dynamic> json) =>
    LoginStatusModel(
      isLoginAllowed: json['isLoginAllowed'] as bool?,
      needsTradingInfoEntry: json['needsTradingInfoEntry'] as bool?,
      needsFxAccountOpening: json['needsFxAccountOpening'] as bool?,
      hasAuthenticationMailAddress:
          json['hasAuthenticationMailAddress'] as bool?,
    );

Map<String, dynamic> _$LoginStatusModelToJson(LoginStatusModel instance) =>
    <String, dynamic>{
      'isLoginAllowed': instance.isLoginAllowed,
      'needsTradingInfoEntry': instance.needsTradingInfoEntry,
      'needsFxAccountOpening': instance.needsFxAccountOpening,
      'hasAuthenticationMailAddress': instance.hasAuthenticationMailAddress,
    };

DocumentsReAgreementStatusModel _$DocumentsReAgreementStatusModelFromJson(
        Map<String, dynamic> json) =>
    DocumentsReAgreementStatusModel(
      needsAgreement: json['needsAgreement'] as bool?,
      documentIds: (json['documentIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      shouldRegisterProspectsElectronicDelivary:
          json['shouldRegisterProspectsElectronicDelivary'] as bool?,
      isSkippedAgreement: json['isSkippedAgreement'] as bool?,
    );

Map<String, dynamic> _$DocumentsReAgreementStatusModelToJson(
        DocumentsReAgreementStatusModel instance) =>
    <String, dynamic>{
      'needsAgreement': instance.needsAgreement,
      'documentIds': instance.documentIds,
      'shouldRegisterProspectsElectronicDelivary':
          instance.shouldRegisterProspectsElectronicDelivary,
      'isSkippedAgreement': instance.isSkippedAgreement,
    };
