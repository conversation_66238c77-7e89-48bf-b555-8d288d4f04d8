import 'package:json_annotation/json_annotation.dart';

part 'annnounce_model.g.dart';

@JsonSerializable()
class AnnnounceEffectiveModel {
  const AnnnounceEffectiveModel({
    this.content,
  });
  final String? content;

  factory AnnnounceEffectiveModel.fromJson(Map<String, dynamic> json) => _$AnnnounceEffectiveModelFromJson(json);

  Map<String, dynamic> toJson() => _$AnnnounceEffectiveModelToJson(this);
}
