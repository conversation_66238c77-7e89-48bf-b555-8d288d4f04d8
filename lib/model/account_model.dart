import 'package:json_annotation/json_annotation.dart';

part 'account_model.g.dart';

@JsonSerializable()
class LoginStatusModel {
  const LoginStatusModel({
    this.isLoginAllowed,
    this.needsTradingInfoEntry,
    this.needsFxAccountOpening,
    this.hasAuthenticationMailAddress,
  });
  final bool? isLoginAllowed;
  final bool? needsTradingInfoEntry;
  final bool? needsFxAccountOpening;
  final bool? hasAuthenticationMailAddress;

  factory LoginStatusModel.fromJson(Map<String, dynamic> json) => _$LoginStatusModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoginStatusModelToJson(this);
}

@JsonSerializable()
class DocumentsReAgreementStatusModel {
  const DocumentsReAgreementStatusModel({
    this.needsAgreement,
    this.documentIds,
    this.shouldRegisterProspectsElectronicDelivary,
    this.isSkippedAgreement,
  });
  final bool? needsAgreement;
  final List<String>? documentIds;
  final bool? shouldRegisterProspectsElectronicDelivary;
  final bool? isSkippedAgreement;

  factory DocumentsReAgreementStatusModel.fromJson(Map<String, dynamic> json) => _$DocumentsReAgreementStatusModelFromJson(json);

  Map<String, dynamic> toJson() => _$DocumentsReAgreementStatusModelToJson(this);
}
