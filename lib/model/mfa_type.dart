class MfaType {
  const MfaType({
    this.isMfaPush = false,
    this.isMfaEmail = false,
    this.isMfaRecovery = false,
  });
  final bool isMfaPush;
  final bool isMfaEmail;
  final bool isMfaRecovery;

  bool get isMfa => isMfaPush || isMfaEmail || isMfaRecovery;

  MfaType copyWith({
    bool? isMfaPush,
    bool? isMfaEmail,
    bool? isMfaRecovery,
  }) {
    return MfaType(
      isMfaPush: isMfaPush ?? this.isMfaPush,
      isMfaEmail: isMfaEmail ?? this.isMfaEmail,
      isMfaRecovery: isMfaRecovery ?? this.isMfaRecovery,
    );
  }
}
