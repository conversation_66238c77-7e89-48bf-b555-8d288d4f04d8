// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'timeline_webview_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TimelineWebViewModel _$TimelineWebViewModelFromJson(
        Map<String, dynamic> json) =>
    TimelineWebViewModel(
      screenId: json['screenId'] as String,
      data: TimelineWebViewDataModel.fromJson(
          json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TimelineWebViewModelToJson(
        TimelineWebViewModel instance) =>
    <String, dynamic>{
      'screenId': instance.screenId,
      'data': instance.data,
    };

TimelineWebViewDataModel _$TimelineWebViewDataModelFromJson(
        Map<String, dynamic> json) =>
    TimelineWebViewDataModel(
      id: json['id'] as String,
      type: json['type'] as String,
    );

Map<String, dynamic> _$TimelineWebViewDataModelToJson(
        TimelineWebViewDataModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
    };
