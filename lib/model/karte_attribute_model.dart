import 'package:json_annotation/json_annotation.dart';

part 'karte_attribute_model.g.dart';

@JsonSerializable()
class KarteAttributeModel {
  const KarteAttributeModel({
    this.title,
    this.body,
    this.url,
    this.targetUrl,
  });
  final String? title;
  final String? body;
  final String? url;
  final String? targetUrl;

  factory KarteAttributeModel.fromJson(Map<String, dynamic> json) => _$KarteAttributeModelFromJson(json);

  Map<String, dynamic> toJson() => _$KarteAttributeModelToJson(this);
}
