// ignore_for_file: non_constant_identifier_names

import 'package:json_annotation/json_annotation.dart';
import 'package:kc_member_site_native/constant/enum.dart';
import 'package:kc_member_site_native/model/login_model.dart';

part 'mfa_model.g.dart';

@JsonSerializable()
class MfaAuthenticatorsModel {
  const MfaAuthenticatorsModel({
    this.authenticator_type,
    this.oob_channel,
    this.active,
    this.id,
    this.name,
  });
  final String? authenticator_type;
  final String? oob_channel;
  final bool? active;
  final String? id;
  final String? name;

  AuthenticatorType? get authenticatorType => AuthenticatorType.from(authenticator_type);
  OobChannel? get oobChannel => OobChannel.from(oob_channel);

  factory MfaAuthenticatorsModel.fromJson(Map<String, dynamic> json) => _$MfaAuthenticatorsModelFromJson(json);

  Map<String, dynamic> toJson() => _$MfaAuthenticatorsModelToJson(this);

  bool get isActive => active ?? false;

  bool get isMfaPush => authenticatorType == AuthenticatorType.oob && oobChannel == OobChannel.auth0 && isActive;
  bool get isMfaEmail => authenticatorType == AuthenticatorType.oob && oobChannel == OobChannel.email && isActive;
  bool get isMfaRecovery => authenticatorType == AuthenticatorType.recoveryCode && isActive;
  bool get isMfaOtp => authenticatorType == AuthenticatorType.otp && isActive;
}

class MfaChallengeModel {
  const MfaChallengeModel({
    this.error,
    this.oob_code,
  });
  final String? oob_code;
  final LoginErrorModel? error;
  bool get isSuccessful => error == null;

  factory MfaChallengeModel.fromJson(Map<String, dynamic> json) {
    return MfaChallengeModel(
      oob_code: json['oob_code'] as String?,
    );
  }
}

class MfaTypeModel {
  const MfaTypeModel({
    this.isMfaPush = false,
    this.isMfaEmail = false,
    this.isMfaRecovery = false,
  });

  final bool isMfaPush;
  final bool isMfaEmail;
  final bool isMfaRecovery;

  MfaTypeModel copyWith({
    bool? isMfaPush,
    bool? isMfaEmail,
    bool? isMfaRecovery,
  }) {
    return MfaTypeModel(
      isMfaPush: isMfaPush ?? this.isMfaPush,
      isMfaEmail: isMfaEmail ?? this.isMfaEmail,
      isMfaRecovery: isMfaRecovery ?? this.isMfaRecovery,
    );
  }
}
