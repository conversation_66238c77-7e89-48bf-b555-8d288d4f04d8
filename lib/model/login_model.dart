// ignore_for_file: non_constant_identifier_names

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:kc_member_site_native/constant/base.dart';

part 'login_model.g.dart';

@JsonSerializable()
class CookiesModel {
  const CookiesModel({
    this.newsite,
    this.se,
    this.sub,
    this.xt,
    this.token,
    this.siteid,
    this.mfa_session_id,
  });

  final String? newsite;
  final String? se;
  final String? sub;
  final String? xt;
  final String? token;
  final String? siteid;
  final String? mfa_session_id;

  factory CookiesModel.fromJson(Map<String, dynamic> json) => _$CookiesModelFromJson(json);
  Map<String, dynamic> toJson() => _$CookiesModelToJson(this);
}

/// The regex pattern for splitting the set-cookie header.
final _regexSplitSetCookies = RegExp(
  r"""(?<!expires=\w{3}|"|')\s*,\s*(?!"|')""",
  caseSensitive: false,
);

class LoginModel {
  const LoginModel({
    this.error,
    this.cookiesLite,
    this.cookies,
    this.recovery_code,
  });
  final LoginErrorModel? error;
  bool get isSuccessful => error == null;

  final CookiesModel? cookiesLite;
  final List<Cookie>? cookies;
  final String? recovery_code;

  factory LoginModel.fromCookiesString(String? cookiesString) {
    final cookieList = cookiesString?.split(_regexSplitSetCookies) ?? [];
    final List<Cookie> cookies = [];
    Map<String, dynamic> cookiesLiteMap = {};
    for (var e in cookieList) {
      try {
        final cookie = Cookie.fromSetCookieValue(e);
        cookie.domain ??= Base.bffDomain;
        cookies.add(cookie);
        cookiesLiteMap[cookie.name] = cookie.value;
        debugPrint("__cookie ${cookie.name}:${cookie.secure}");
      } catch (e) {
        debugPrint(e.toString());
      }
    }
    return LoginModel(
      cookiesLite: CookiesModel.fromJson(cookiesLiteMap),
      cookies: cookies,
    );
  }

  factory LoginModel.fromJson(Map<String, dynamic> json) {
    return LoginModel(
      recovery_code: json['recovery_code'] as String?,
    );
  }

  LoginModel copyWith({
    CookiesModel? cookiesLite,
    List<Cookie>? cookies,
    String? recovery_code,
    LoginErrorModel? error,
  }) {
    return LoginModel(
      cookiesLite: cookiesLite ?? this.cookiesLite,
      cookies: cookies ?? this.cookies,
      recovery_code: recovery_code ?? this.recovery_code,
      error: error ?? this.error,
    );
  }
}

class LoginErrorModel {
  const LoginErrorModel({
    this.error,
    this.error_description,
    this.mfa_token,
    this.httpStatus,
    this.email,
    this.email_verified,
  });
  final String? error;
  final String? error_description;
  final String? mfa_token;
  final int? httpStatus;
  final String? email;
  final bool? email_verified;

  ErrorCode? get errorCode => ErrorCode.from(
        httpStatus: httpStatus,
        error: error,
        description: error_description?.trim(),
      );

  factory LoginErrorModel.fromJson(dynamic json, int? httpStatus) {
    if (json is String && json.isEmpty) {
      json = {};
    }
    return LoginErrorModel(
      error: json['error'] as String?,
      error_description: json['error_description'] as String?,
      mfa_token: json['mfa_token'] as String?,
      email: json['email'] as String?,
      email_verified: json['email_verified'] as bool?,
      httpStatus: httpStatus,
    );
  }
}

enum ErrorCode {
  expiredToken("mfa_token is expired"),
  tooManyPushNotify("Too many push notifications requested for the user. Wait for some minutes before retrying."),
  tooManyEmail("Too many emails sent by the user. Wait for some minutes before retrying."),
  authPending("Authorization pending: please repeat the request in a few seconds."),
  pollingTooFast("You are polling too fast, please slow down"),
  transactionNotFound("Transaction not found."),
  invalidOtp("Invalid otp_code."),
  invalidBindingCode("Invalid binding_code."),
  tooManyFailCode("Too many failed codes. Wait for some minutes before retrying."),
  authRejected("MFA Authorization rejected."),

  wrongAccount("口座番号またはパスワードに誤りがあります。"),
  wrongAccountKeycloak("ユーザ認証に失敗した。（username または password 誤り ）"),
  userBlock("user is blocked"),
  mfaRequest("Multifactor authentication required"),
  auth0Stopped("connection is disabled"),
  tooManyAttempts("too_many_attempts");

  final String value;
  const ErrorCode(this.value);

  static ErrorCode? from({int? httpStatus, String? error, String? description}) {
    switch (httpStatus) {
      case 400:
        if (error == "invalid_request") {
          if (description?.contains(ErrorCode.auth0Stopped.value) ?? false) {
            return ErrorCode.auth0Stopped;
          }
          if (description == ErrorCode.transactionNotFound.value) {
            return ErrorCode.transactionNotFound;
          }
        }
        if (error == "authorization_pending") {
          if (description == ErrorCode.authPending.value) {
            return ErrorCode.authPending;
          }
        }
        break;
      case 401:
        if (error == "invalid_grant") {
          return ErrorCode.wrongAccountKeycloak;
        }
        break;
      case 403:
        if (error == "invalid_grant") {
          if (description == ErrorCode.wrongAccount.value) {
            return ErrorCode.wrongAccount;
          }
          if (description == ErrorCode.userBlock.value) {
            return ErrorCode.userBlock;
          }
          if (description == ErrorCode.invalidOtp.value) {
            return ErrorCode.invalidOtp;
          }
          if (description == ErrorCode.invalidBindingCode.value) {
            return ErrorCode.invalidBindingCode;
          }
          if (description == ErrorCode.authRejected.value) {
            return ErrorCode.authRejected;
          }
        }
        if (error == "mfa_required") {
          if (description == ErrorCode.mfaRequest.value) {
            return ErrorCode.mfaRequest;
          }
        }
        if (error == "expired_token") {
          if (description == ErrorCode.expiredToken.value) {
            return ErrorCode.expiredToken;
          }
        }
        break;
      case 429:
        if (error == ErrorCode.tooManyAttempts.value) {
          return ErrorCode.tooManyAttempts;
        }
        if (error == "slow_down") {
          if (description == ErrorCode.pollingTooFast.value) {
            return ErrorCode.pollingTooFast;
          }
        }
        if (error == "invalid_request") {
          if (description == ErrorCode.tooManyFailCode.value) {
            return ErrorCode.tooManyFailCode;
          }
          if (description == ErrorCode.tooManyPushNotify.value) {
            return ErrorCode.tooManyPushNotify;
          }
          if (description == ErrorCode.tooManyEmail.value) {
            return ErrorCode.tooManyEmail;
          }
        }
        break;
    }
    return null;
  }
}
