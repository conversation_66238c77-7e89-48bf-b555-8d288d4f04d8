import 'package:json_annotation/json_annotation.dart';

part 'login_status_model.g.dart';

@JsonSerializable()
class LoginStatusModel {
  const LoginStatusModel({
    this.isAvailable,
    this.message,
  });
  final bool? isAvailable;
  final String? message;

  factory LoginStatusModel.fromJson(Map<String, dynamic> json) => _$LoginStatusModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoginStatusModelToJson(this);
}
