import 'package:json_annotation/json_annotation.dart';

part 'timeline_webview_model.g.dart';

@JsonSerializable()
class TimelineWebViewModel {
  String screenId;
  TimelineWebViewDataModel data;

  TimelineWebViewModel({required this.screenId, required this.data});

  factory TimelineWebViewModel.fromJson(Map<String, dynamic> json) => _$TimelineWebViewModelFromJson(json);

  Map<String, dynamic> toJson() => _$TimelineWebViewModelToJson(this);
}

@JsonSerializable()
class TimelineWebViewDataModel {
  String id;
  String type;

  TimelineWebViewDataModel({required this.id, required this.type});

  factory TimelineWebViewDataModel.fromJson(Map<String, dynamic> json) => _$TimelineWebViewDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$TimelineWebViewDataModelToJson(this);
}
