// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'geo_location_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GeoLocationModel _$GeoLocationModelFromJson(Map<String, dynamic> json) =>
    GeoLocationModel(
      result: json['result'] as bool?,
      message: json['message'] as String?,
      data: json['data'] == null
          ? null
          : GeoLocationData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$GeoLocationModelToJson(GeoLocationModel instance) =>
    <String, dynamic>{
      'result': instance.result,
      'message': instance.message,
      'data': instance.data,
    };

GeoLocationData _$GeoLocationDataFromJson(Map<String, dynamic> json) =>
    GeoLocationData(
      fromJapan: json['fromJapan'] as bool?,
      country: json['country'] as String?,
    );

Map<String, dynamic> _$GeoLocationDataToJson(GeoLocationData instance) =>
    <String, dynamic>{
      'fromJapan': instance.fromJapan,
      'country': instance.country,
    };
