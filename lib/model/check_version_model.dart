import 'package:json_annotation/json_annotation.dart';

part 'check_version_model.g.dart';

enum UpdateType {
  unspecified('UPDATE_TYPE_UNSPECIFIED'),
  unnecessary('UPDATE_TYPE_UNNECESSARY'),
  voluntarily('UPDATE_TYPE_VOLUNTARILY'),
  imperative('UPDATE_TYPE_IMPERATIVE');

  const UpdateType(this.name);
  final String name;
}

@JsonSerializable()
class CheckVersionModel {
  String updateType;
  String updateMessage;

  CheckVersionModel({required this.updateType, required this.updateMessage});

  factory CheckVersionModel.fromJson(Map<String, dynamic> json) => _$CheckVersionModelFromJson(json);

  Map<String, dynamic> toJson() => _$CheckVersionModelToJson(this);
}
