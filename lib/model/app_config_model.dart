// ignore_for_file: non_constant_identifier_names

import 'package:json_annotation/json_annotation.dart';
import 'package:kc_member_site_native/converter/index.dart';

part 'app_config_model.g.dart';

@JsonSerializable()
class AppConfigModel {
  const AppConfigModel({
    required this.app_config,
    this.appReview,
  });
  final AppConfigContentModel app_config;
  final AppReviewModel? appReview;

  factory AppConfigModel.fromJson(Map<String, dynamic> json) => _$AppConfigModelFromJson(json);

  Map<String, dynamic> toJson() => _$AppConfigModelToJson(this);
}

@JsonSerializable()
class AppConfigContentModel {
  AppConfigDomainWiModel wl;
  AppConfigDomainOpenwithbrowserModel openwithbrowser;

  AppConfigContentModel({required this.wl, required this.openwithbrowser});

  factory AppConfigContentModel.fromJson(Map<String, dynamic> json) => _$AppConfigContentModelFromJson(json);

  Map<String, dynamic> toJson() => _$AppConfigContentModelToJson(this);
}

@JsonSerializable()
class AppConfigDomainWiModel {
  List<String> domain;

  AppConfigDomainWiModel({required this.domain});

  factory AppConfigDomainWiModel.fromJson(Map<String, dynamic> json) => _$AppConfigDomainWiModelFromJson(json);

  Map<String, dynamic> toJson() => _$AppConfigDomainWiModelToJson(this);
}

@JsonSerializable()
class AppConfigDomainOpenwithbrowserModel {
  List<String> urlincludes;

  AppConfigDomainOpenwithbrowserModel({required this.urlincludes});

  factory AppConfigDomainOpenwithbrowserModel.fromJson(Map<String, dynamic> json) => _$AppConfigDomainOpenwithbrowserModelFromJson(json);

  Map<String, dynamic> toJson() => _$AppConfigDomainOpenwithbrowserModelToJson(this);
}

@JsonSerializable()
class AppReviewModel {
  const AppReviewModel({
    this.startDate,
    this.endDate,
  });

  @DateTimeConverter()
  final DateTime? startDate;

  @DateTimeConverter()
  final DateTime? endDate;

  factory AppReviewModel.fromJson(Map<String, dynamic> json) => _$AppReviewModelFromJson(json);

  Map<String, dynamic> toJson() => _$AppReviewModelToJson(this);
}
