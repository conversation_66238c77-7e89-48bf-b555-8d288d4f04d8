import 'package:json_annotation/json_annotation.dart';

part 'geo_location_model.g.dart';

@JsonSerializable()
class GeoLocationModel {
  const GeoLocationModel({
    this.result,
    this.message,
    this.data,
  });
  final bool? result;
  final String? message;
  final GeoLocationData? data;

  factory GeoLocationModel.fromJson(Map<String, dynamic> json) => _$GeoLocationModelFromJson(json);

  Map<String, dynamic> toJson() => _$GeoLocationModelToJson(this);
}

@JsonSerializable()
class GeoLocationData {
  const GeoLocationData({
    this.fromJapan,
    this.country,
  });
  final bool? fromJapan;
  final String? country;

  factory GeoLocationData.fromJson(Map<String, dynamic> json) => _$GeoLocationDataFromJson(json);

  Map<String, dynamic> toJson() => _$GeoLocationDataToJson(this);
}
