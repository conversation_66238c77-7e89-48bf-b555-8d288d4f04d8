// ignore_for_file: non_constant_identifier_names

import 'package:json_annotation/json_annotation.dart';

part 'timeline_payload_model.g.dart';

@JsonSerializable()
class TimelinePayloadModel {
  TimelineIdModel TimeLineID;
  CustomsortDivisionModel CustomsortDivision;

  TimelinePayloadModel({required this.TimeLineID, required this.CustomsortDivision});

  factory TimelinePayloadModel.fromJson(Map<String, dynamic> json) => _$TimelinePayloadModelFromJson(json);

  Map<String, dynamic> toJson() => _$TimelinePayloadModelToJson(this);
}

@JsonSerializable()
class TimelineIdModel {
  String StringValue;

  TimelineIdModel({required this.StringValue});

  factory TimelineIdModel.fromJson(Map<String, dynamic> json) => _$TimelineIdModelFromJson(json);

  Map<String, dynamic> toJson() => _$TimelineIdModelToJson(this);
}

@JsonSerializable()
class CustomsortDivisionModel {
  String StringValue;

  CustomsortDivisionModel({required this.StringValue});

  factory CustomsortDivisionModel.fromJson(Map<String, dynamic> json) => _$CustomsortDivisionModelFromJson(json);

  Map<String, dynamic> toJson() => _$CustomsortDivisionModelToJson(this);
}
