import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:webview_cookie_manager/webview_cookie_manager.dart';
import 'package:webview_flutter/webview_flutter.dart';

class SessionCookie {
  const SessionCookie({this.se, this.xt, required this.cookies});
  final String? se;
  final String? xt;
  final List<Cookie> cookies;
  bool get hasSession => (se?.isNotEmpty ?? false) && (xt?.isNotEmpty ?? false);
}

final cookieManager = WebviewCookieManager();

/// Remove all cookies, session cookies storage
Future<void> clearSession() async {
  Completer<void> completer = Completer();
  void setComplete() {
    if (!completer.isCompleted) {
      completer.complete();
    }
  }

  // iOS < 12.4 hangs app with WebviewCookieManager().clearCookies()
  // => Skip waiting if waiting more than 2 seconds
  Future.delayed(const Duration(milliseconds: 2000), setComplete);
  if (Platform.isIOS) {
    // fix(KCMSR-4794): because WebviewCookieManager().clearCookies synchronous handling not working properly
    WebViewCookieManager().clearCookies().then((_) => setComplete());
  } else {
    // Why not use the same method as iOS?
    // => Because it already works stably on Android and to limit the scope of influence
    cookieManager.clearCookies().then((_) => setComplete());
  }
  await completer.future;
}

SessionCookie getSessionCookie(List<Cookie>? cookies) {
  if (cookies == null || cookies.isEmpty) return const SessionCookie(cookies: []);
  String? se, xt;
  for (final item in cookies) {
    debugPrint('${item.name}=${item.value}');
    if (item.name == 'se') se = item.value;
    if (item.name == 'xt') xt = item.value;
  }
  return SessionCookie(se: se, xt: xt, cookies: cookies);
}
