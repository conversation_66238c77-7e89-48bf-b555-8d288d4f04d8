import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:kc_member_site_native/app.dart';
import 'package:kc_member_site_native/constant/base.dart';

class FirebaseAnalyticsUtils {
  const FirebaseAnalyticsUtils._();
  static const screenViewEvent = 'screen_view';
  static const screenNameKey = 'screen_name';
  static const screenClassKey = 'screen_class';
  static const prefixWebParamKey = 'p_';
  static const eventNameKey = 'eventName';
  static const webSchemeChannel = 'fatracking';

  static void faScreenTracking({String? path}) {
    final screenName = Base.mapScreenClass[path];
    if (screenName != null) {
      final screenClass = path?.replaceFirst(r'/native', '/native/${Base.platformName}');
      debugPrint("____faScreenTracking Path $path");
      MyApp.analytics.logEvent(
        name: screenViewEvent,
        parameters: {
          screenNameKey: screenName,
          screenClassKey: screenClass ?? "",
        },
      ).catchError(
        (Object error) {
          debugPrint('$error');
        },
        test: (Object error) => error is PlatformException,
      );
    }
  }

  static void faTrackingFromWeb(Map<String, String> params) {
    String? eventName = params[eventNameKey];
    if (eventName == null) return;

    params.remove(eventNameKey);

    Map<String, String> parameters = {};
    params.forEach((key, value) {
      String rawKey = key;
      if (key.startsWith(prefixWebParamKey)) {
        rawKey = key.substring(prefixWebParamKey.length);
      }
      parameters[rawKey] = value;
    });

    debugPrint("faTrackingFromWeb, $eventName, ${parameters.toString()}");
    MyApp.analytics.logEvent(name: eventName, parameters: parameters).catchError(
      (Object error) {
        debugPrint('$error');
      },
      test: (Object error) => error is PlatformException,
    );
  }

  static void faAutoLoginTracking({
    required String username,
    required bool isAutoLogin,
    required bool isSaveUsername,
    required bool isSavePassword,
  }) {
    debugPrint("____faAutoLoginTracking $isAutoLogin");
    MyApp.analytics.logEvent(
      name: "auto_login",
      parameters: {
        "userID": username,
        "isAutoLogin": isAutoLogin.toString(),
        "isSaveUsername": isSaveUsername.toString(),
        "isSavePassword": isSavePassword.toString(),
      },
    ).catchError(
      (Object error) {
        debugPrint('$error');
      },
      test: (Object error) => error is PlatformException,
    );
  }
}
