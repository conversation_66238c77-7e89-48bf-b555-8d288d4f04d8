import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:uni_links/uni_links.dart';

class UniLinksUtil {
  UniLinksUtil._();
  static final UniLinksUtil _instance = UniLinksUtil._();

  factory UniLinksUtil() {
    return _instance;
  }

  bool _initialUriIsHandled = false;
  StreamSubscription<Uri?>? _sub;

  void unsubscribe() {
    _sub?.cancel();
  }

  void _handleIncomingLinks(void Function(Uri link) onReceiveLink) {
    if (!kIsWeb) {
      // It will handle app links while the app is already started - be it in
      // the foreground or in the background.
      _sub = uriLinkStream.listen(
        (Uri? uri) {
          if (uri != null) {
            onReceiveLink(uri);
          }
        },
        onError: (Object err) {
          //
        },
      );
    }
  }

  Future<void> initUniLinks(void Function(Uri link) onReceiveLink) async {
    _handleIncomingLinks(onReceiveLink);
    if (!_initialUriIsHandled) {
      _initialUriIsHandled = true;
      try {
        final uri = await getInitialUri();
        if (uri != null) {
          onReceiveLink(uri);
        }
      } on PlatformException {
        // Platform messages may fail but we ignore the exception
      } on FormatException {
        //
      }
    }
  }
}
