import 'dart:io';

import 'package:move_to_background/move_to_background.dart';

class PopScopeUtil {
  factory PopScopeUtil() {
    return _singleton;
  }

  PopScopeUtil._internal();

  static final PopScopeUtil _singleton = PopScopeUtil._internal();

  void onWillPop(bool didPop, Object? result) async {
    if (didPop) {
      return;
    }
    if (Platform.isAndroid) {
      MoveToBackground.moveTaskToBack();
    }
  }
}
