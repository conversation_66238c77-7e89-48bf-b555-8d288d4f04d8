import 'dart:async';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';

class DeviceInfoUtil {
  factory DeviceInfoUtil() {
    return _singleton;
  }

  DeviceInfoUtil._internal();

  static final _singleton = DeviceInfoUtil._internal();
  final _deviceInfo = DeviceInfoPlugin();
  late AndroidDeviceInfo _androidDeviceInfo;

  /// https://pub.dev/packages/webview_flutter_android#texture-layer-hybrid-composition
  static const versionSupportHybrid = 23;

  Future<void> init() async {
    if (Platform.isAndroid) {
      _androidDeviceInfo = await _deviceInfo.androidInfo;
    }
  }

  bool shouldHybridComposition() {
    if (!Platform.isAndroid) {
      return false;
    }
    final sdkInt = _androidDeviceInfo.version.sdkInt;
    return sdkInt >= DeviceInfoUtil.versionSupportHybrid;
  }
}
