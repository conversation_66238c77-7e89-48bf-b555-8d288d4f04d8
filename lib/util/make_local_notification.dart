import 'dart:convert';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:kc_member_site_native/model/karte_attribute_model.dart';
import 'package:kc_member_site_native/util/karte_util.dart';

import '../model/timeline_payload_model.dart';
import 'run_javascript.dart';

final flnp = FlutterLocalNotificationsPlugin();

Future makeLocalNotification(RemoteNotification notification, Map data) async {
  debugPrint("makeLocalNotification");
  final timelineId = data['TimeLineID'];
  final customsortDivision = data['CustomsortDivision'];
  final remapData = {
    'TimeLineID': timelineId is String ? json.decode(timelineId) : timelineId,
    'CustomsortDivision': customsortDivision is String ? json.decode(customsortDivision) : customsortDivision
  };
  _makeLocalNotification(
    title: notification.title,
    body: notification.body,
    data: remapData,
  );
}

Future makeKarteLocalNotification(Map data) async {
  final notification = KarteAttributeModel.fromJson(json.decode(data['krt_attributes']));
  _makeLocalNotification(
    title: notification.title,
    body: notification.body,
    data: data,
  );
}

Future _makeLocalNotification({
  required String? title,
  required String? body,
  required Map<dynamic, dynamic> data,
}) async {
  final payloadMap = {
    'notification': {'title': title, 'body': body},
    'data': data
  };
  final payloadStr = json.encode(payloadMap);
  debugPrint(payloadStr);
  await flnp
      .initialize(
          const InitializationSettings(
              // [iOS] Breaking change Removed onDidReceiveLocalNotification callback as this was only relevant on iOS versions older than 10
              // iOS: DarwinInitializationSettings(onDidReceiveLocalNotification: onDidReceiveLocation),
              iOS: DarwinInitializationSettings(),
              android: AndroidInitializationSettings('@drawable/ic_notification')),
          onDidReceiveNotificationResponse: onSelectNotification)
      .then((value) => flnp.show(
          data.hashCode,
          title,
          body,
          NotificationDetails(
              android: AndroidNotificationDetails(
            '0',
            'none',
            importance: Importance.high,
            priority: Priority.high,
            styleInformation: BigTextStyleInformation(body ?? ""),
          )),
          payload: payloadStr));
}

void onSelectNotification(NotificationResponse? notificationResponse) async {
  debugPrint('onSelectNotification: $notificationResponse');
  final payload = notificationResponse?.payload ?? '';
  final remoteMessage = RemoteMessage.fromMap(json.decode(payload));
  if (await KarteUtil().isKartePush(remoteMessage)) {
    final data = KarteAttributeModel.fromJson(remoteMessage.data);
    KarteUtil().postNotifyKarteData(data.targetUrl);
    return;
  }
  final timelinePayloadModel = TimelinePayloadModel.fromJson(remoteMessage.data);
  postNotifyData(timelinePayloadModel);
}
