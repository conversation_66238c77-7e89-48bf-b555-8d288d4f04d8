import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:kc_member_site_native/constant/app_config.dart';
import 'package:kc_member_site_native/constant/storage.dart';
import 'package:kc_member_site_native/service/secure_storage_service.dart';
import 'package:kc_member_site_native/util/app_info.dart';
import 'package:kc_member_site_native/util/date_util.dart';
import 'package:kc_member_site_native/util/shared_preference.dart';
import 'package:kc_member_site_native/view_model/login_view_model.dart';
import 'package:provider/provider.dart';

class AppReview {
  factory AppReview() {
    return _singleton;
  }

  AppReview._internal();

  static final AppReview _singleton = AppReview._internal();
  final _inAppReview = InAppReview.instance;

  static const targetUrls = [
    "/mobile/mypage/performance",
    "/iPhone/Trade/ToushinBuy/IB01103.asp",
    "/iPhone/Trade/ToushinSell/IS01103.asp",
  ];

  late SecureStorageServiceImpl _secureStorage;

  Future<void> init(SecureStorageServiceImpl secureStorage) async {
    _secureStorage = secureStorage;
    final storageBuildNumber = sharedPreferences.getString(StorageKey.buildNumber);
    final currentBuildNumber = AppInfo().packageInfo.buildNumber;
    if (currentBuildNumber != storageBuildNumber) {
      await Future.wait([
        secureStorage.delete(StorageKey.appReviewHistory),
        sharedPreferences.setString(StorageKey.buildNumber, currentBuildNumber),
        sharedPreferences.setString(StorageKey.installDate, DateTime.now().toIso8601String()),
      ]);
    }
  }

  String? matchUrl(String url) {
    for (var targetUrl in targetUrls) {
      if (url.contains(targetUrl)) return targetUrl;
    }
    return null;
  }

  bool matchDate(
    DateTime currentDate, {
    required DateTime? startDate,
    required DateTime? endDate,
  }) {
    if (startDate == null || endDate == null) return false;
    return currentDate.isAfter(startDate) && currentDate.isBefore(endDate);
  }

  bool checkInstallDate({required DateTime currentDate, String? installDateString}) {
    if (installDateString == null) return true;
    final installDate = DateTime.tryParse(installDateString);
    if (installDate == null) return false;
    final diff = daysBetween(installDate, currentDate);
    return diff >= 6;
  }

  String parseMessage(String msg) {
    try {
      final data = jsonDecode(msg) as Map<String, dynamic>;
      return data["url"];
    } catch (e) {
      return "";
    }
  }

  /// Only iOS
  Future<void> conditionsShowReview({
    required BuildContext context,
    required String url,
  }) async {
    if (Platform.isAndroid) return;
    final reviewUrl = AppReview().matchUrl(url);
    if (reviewUrl == null) {
      return;
    }
    final currentDate = DateTime.now();
    final configReview = appConfigModel.appReview;
    if (!matchDate(
      currentDate,
      startDate: configReview?.startDate,
      endDate: configReview?.endDate,
    )) {
      return;
    }
    final installDateString = sharedPreferences.getString(StorageKey.installDate);
    if (!checkInstallDate(currentDate: currentDate, installDateString: installDateString)) {
      return;
    }
    final username = context.read<LoginViewModel>().username;
    final history = await _secureStorage.getAppReviewHistory();

    List<String>? historyUrls = history[username];
    if (historyUrls?.contains(reviewUrl) == true) {
      return;
    }
    historyUrls ??= [];
    historyUrls.add(reviewUrl);
    history[username] = historyUrls;
    _secureStorage.saveAppReviewHistory(history);
    _inAppReview.requestReview();
  }
}
