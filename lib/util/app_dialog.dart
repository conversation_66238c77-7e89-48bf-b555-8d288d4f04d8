import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/constant/base.dart';
import 'package:kc_member_site_native/extensions/build_context_extensions.dart';
import 'package:kc_member_site_native/gen/assets.gen.dart';
import 'package:kc_member_site_native/themes/app_colors.dart';
import 'package:kc_member_site_native/themes/app_text_theme.dart';
import 'package:kc_member_site_native/util/in_app_browser.dart';
import 'package:kc_member_site_native/widgets/single_touch_container.dart';

class ActionProps {
  const ActionProps({required this.child, this.onPressed});
  final void Function(BuildContext ctx)? onPressed;
  final Widget child;
}

Future<void> showNetworkErrorConnectivity(BuildContext context, List<ActionProps> actions) async =>
    await showMessageActionDialog(context, context.l10n.meNetworkErrorConnectivity, actions);

Future<void> showNetworkErrorTimeout(BuildContext context, List<ActionProps> actions) async =>
    await showMessageActionDialog(context, context.l10n.meNetworkErrorTimeout, actions);

Future<void> showNetworkErrorSystem(BuildContext context, List<ActionProps> actions) async =>
    await showMessageActionDialog(context, context.l10n.meNetworkErrorSystem, actions);

Future<void> showNetworkErrorMaintenance(BuildContext context, List<ActionProps> actions) async =>
    await showMessageActionDialog(context, context.l10n.meNetworkErrorMaintenance, actions);

Future<void> showPushNotificationGrant(BuildContext context, List<ActionProps> actions) async =>
    await showMessageActionDialog(context, context.l10n.mePushNotificationGrant, actions);

Future<void> showPushNotificationSettingError(BuildContext context, List<ActionProps> actions) async =>
    await showMessageActionDialog(context, context.l10n.mePushNotificationSettingError, actions);

Future<void> showInvalidSession(BuildContext context, List<ActionProps> actions) async =>
    await showMessageActionDialog(context, context.l10n.meInvalidSession, actions);

Future<void> showJailbreakDetect(BuildContext context, List<ActionProps> actions) async =>
    await showMessageActionDialog(context, context.l10n.me01, actions);

Future<T?> showMessageActionDialog<T>(
  BuildContext context,
  String message,
  List<ActionProps> actions, {
  String? title,
}) {
  return showDialog(
    context: context,
    builder: (ctx) => PopScope(
      canPop: false,
      child: SingleTouchContainer(
        child: AlertDialog(
          title: title != null ? Text(title) : null,
          content: Text(message),
          scrollable: true,
          actions: actions
              .map((action) => TextButton(
                    onPressed: () => action.onPressed?.call(ctx),
                    child: action.child,
                  ))
              .toList(),
        ),
      ),
    ),
  );
}

class DialogUtils {
  DialogUtils._();

  static bool isShowingDl04 = false;

  static Future<void> showDl00(BuildContext context, String message) {
    return showMessageActionDialog(context, message, [
      ActionProps(
        onPressed: (_) {
          launchUrlString(Base.spWebLoginUrl);
        },
        child: Text(context.l10n.webVersion),
      ),
    ]);
  }

  static Future<void> showDl04(BuildContext context, String message) {
    isShowingDl04 = true;
    return showMessageActionDialog(context, message, [
      ActionProps(
        onPressed: (ctx) {
          isShowingDl04 = false;
          Navigator.pop(ctx);
        },
        child: Text(context.l10n.close),
      )
    ]);
  }

  static Future<void> showComplete(
    BuildContext context, {
    required String message,

    /// Auto close after x seconds
    required int closeAfter,
  }) {
    bool isClose = false;
    BuildContext ctx = context;
    Future.delayed(Duration(seconds: closeAfter), () {
      if (isClose || !ctx.mounted) return;
      Navigator.pop(ctx);
    });
    return showDialog(
      context: context,
      barrierColor: AppColors.dialogBg,
      builder: (ctx) {
        ctx = ctx;
        return GestureDetector(
          onTap: () {
            if (isClose) return;
            Navigator.pop(ctx);
          },
          child: AlertDialog(
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Assets.svgs.done.svg(width: 75.w, height: 75.w),
                SizedBox(height: 20.w),
                Text(
                  message,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16.w,
                    fontWeight: FontWeight.bold,
                    color: AppColors.gray500Color,
                    height: AppTextTheme.height24_16,
                  ),
                )
              ],
            ),
            scrollable: true,
            actionsPadding: EdgeInsets.zero,
            contentPadding: EdgeInsets.symmetric(vertical: 30.w, horizontal: 15),
            insetPadding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 24.0),
            shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(11))),
            backgroundColor: AppColors.mainBgColor,
          ),
        );
      },
    ).then((_) {
      isClose = true;
    });
  }
}
