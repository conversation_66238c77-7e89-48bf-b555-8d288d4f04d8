import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_web_browser/flutter_web_browser.dart' as browser;
import 'package:kc_member_site_native/app.dart';
import 'package:kc_member_site_native/util/firebase_analytics.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';

class MyChromeSafariBrowser extends ChromeSafariBrowser {
  final ModalPresentationStyle presentationStyle;
  MyChromeSafariBrowser(this.presentationStyle);

  String? prevPath = "";
  bool get isOverFullScreen => presentationStyle == ModalPresentationStyle.OVER_FULL_SCREEN;

  @override
  void onOpened() {
    if (isOverFullScreen) {
      prevPath = MyApp.observer.currentPath;
      MyApp.observer.setCurrentPath = id;
      debugPrint("____currentPath $id");
    }
    super.onOpened();
  }

  @override
  void onClosed() {
    if (isOverFullScreen) {
      MyApp.observer.setCurrentPath = prevPath;
      debugPrint("____currentPath ${MyApp.observer.currentPath}");
      // Detect event page appear after close SFSafariViewController
      FirebaseAnalyticsUtils.faScreenTracking(path: MyApp.observer.currentPath);
    }
    super.onClosed();
  }
}

Future<void> launchUrlPlatform(BuildContext context, String url) async {
  try {
    final uri = WebUri(url);
    if (Platform.isIOS) {
      const presentationStyle = ModalPresentationStyle.OVER_FULL_SCREEN;
      MyChromeSafariBrowser(presentationStyle).open(
          url: uri,
          settings: ChromeSafariBrowserSettings(
            presentationStyle: presentationStyle,
          ));
    } else {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  } catch (e) {
    debugPrint(e.toString());
  }
}

Future<void> launchUrlString(String url) async {
  try {
    final Uri uri = Uri.parse(url);
    await launchUrl(uri, mode: LaunchMode.externalApplication);
  } catch (e) {
    debugPrint(e.toString());
  }
}

Future<void> openChromeSafariBrowser({
  required BuildContext context,
  required String url,
}) async {
  try {
    if (Platform.isAndroid && !(await canLaunchUrlString(url))) {
      launchUrlString(url);
    }
    return browser.FlutterWebBrowser.openWebPage(
      url: url,
      customTabsOptions: const browser.CustomTabsOptions(
        colorScheme: browser.CustomTabsColorScheme.light,
        shareState: browser.CustomTabsShareState.off,
        instantAppsEnabled: true,
        showTitle: true,
        urlBarHidingEnabled: true,
      ),
      safariVCOptions: const browser.SafariViewControllerOptions(
        barCollapsingEnabled: true,
        dismissButtonStyle: browser.SafariViewControllerDismissButtonStyle.close,
        modalPresentationCapturesStatusBarAppearance: true,
        modalPresentationStyle: browser.UIModalPresentationStyle.overFullScreen,
      ),
    );
  } catch (e) {
    launchUrlString(url);
  }
}
