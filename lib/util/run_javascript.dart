import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:kc_member_site_native/app.dart';
import 'package:kc_member_site_native/constant/base.dart';
import 'package:permission_handler/permission_handler.dart';

import '../model/timeline_payload_model.dart';
import '../model/timeline_webview_model.dart';
import '../screen/webview_screen.dart';
import 'platform_type.dart';

final List<dynamic> queuePostDeviceToken = [];
final List<dynamic> queuePostNotifyData = [];

Future postDeviceToken(String deviceToken) async {
  try {
    final grant = await Permission.notification.isGranted;
    final data = {'deviceToken': grant ? deviceToken : '', 'platformType': PlatformType.getPlatformType};
    final jsonString = json.encode(data);
    if (mainWebViewController == null || isLoading) {
      queuePostDeviceToken.add((() async {
        try {
          debugPrint('___postDeviceToken pop $deviceToken');
          await mainWebViewController!.runJavaScriptReturningResult("postDeviceToken('$jsonString');");
        } catch (_) {}
      }));
    } else {
      await mainWebViewController!.runJavaScriptReturningResult("postDeviceToken('$jsonString');");
      debugPrint('___postDeviceToken $deviceToken');
    }
  } catch (e) {
    debugPrint('___postDeviceToken error ${e.toString()}');
  }
}

Future postPushNotifySettings(String deviceToken) async {
  try {
    final grant = await Permission.notification.isGranted;
    final token = grant ? deviceToken : '';
    await mainWebViewController!.runJavaScriptReturningResult("postPushNotifySettings('$token');");
  } catch (_) {}
}

Future postDeleteDeviceToken(String deviceToken) async {
  try {
    await mainWebViewController!.runJavaScriptReturningResult("postDeleteDeviceToken('$deviceToken');");
  } catch (_) {}
}

void clearPdfStack() {
  if (MyApp.observer.isPdfOpening) {
    MyApp.observer.router?.popForced();
  }
}

void _queuePostNotifyData(String jsonString) {
  queuePostNotifyData.add((() async {
    try {
      debugPrint('___postNotifyData pop $jsonString');
      clearPdfStack();
      await mainWebViewController!.runJavaScriptReturningResult("postNotifyData('$jsonString');");
    } catch (_) {}
  }));
}

Future postNotifyData(TimelinePayloadModel messageAttributes) async {
  if (messageAttributes.TimeLineID.StringValue.isNotEmpty) {
    try {
      final timelineWebViewData = {
        'id': messageAttributes.TimeLineID.StringValue,
        'type': messageAttributes.CustomsortDivision.StringValue
      };
      final timelineWebView = {'screenId': '/mobile/timeline/detail', 'data': timelineWebViewData};
      final timelineWebViewModel = TimelineWebViewModel.fromJson(timelineWebView);
      final jsonString = json.encode(timelineWebViewModel.toJson());
      if (mainWebViewController == null || isLoading) {
        _queuePostNotifyData(jsonString);
      } else {
        debugPrint('_postNotifyData $jsonString');
        final currentUrl = await mainWebViewController?.currentUrl();
        if (!(currentUrl?.startsWith(Base.bffDomainUrl) ?? false)) {
          _queuePostNotifyData(jsonString);
          mainWebViewController?.loadRequest(Uri.parse(Base.homePageUrl));
        } else {
          clearPdfStack();
          await mainWebViewController!.runJavaScriptReturningResult("postNotifyData('$jsonString');");
        }
      }
    } catch (_) {}
  }
}
