import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:karte_core/karte_core.dart';
import 'package:karte_notification/karte_notification.dart' as krt;
import 'package:kc_member_site_native/app.dart';
import 'package:kc_member_site_native/extensions/string_extensions.dart';
import 'package:kc_member_site_native/main.dart';
import 'package:kc_member_site_native/routes/app_router.dart';
import 'package:kc_member_site_native/screen/webview_screen.dart';
import 'package:kc_member_site_native/service/secure_storage_service.dart';
import 'package:kc_member_site_native/util/in_app_browser.dart';
import 'package:kc_member_site_native/util/run_javascript.dart';

class KarteUtil {
  factory KarteUtil() {
    return _singleton;
  }

  KarteUtil._internal();

  static final KarteUtil _singleton = KarteUtil._internal();
  final List<dynamic> _queuePostNotifyKarteData = [];

  void identify({
    required SecureStorageService secureStorage,
    required String userId,
  }) async {
    final usersMap = await secureStorage.getUserIdKarte();

    if (usersMap[userId] != true) {
      if (usersMap.isNotEmpty) {
        // only renewVisitorId when login another account
        KarteApp.renewVisitorId();
      }
      usersMap[userId] = true;
      secureStorage.saveUserIdKarte(usersMap);
    }
    try {
      Tracker.view("login_success");
      Tracker.identifyWithUserId(userId);
    } on Exception catch (e) {
      debugPrint('Tracker Exception ${e.toString()}');
    }
  }

  Future<void> registerFCMToken() async {
    debugPrint('_registerFCMToken: $fcmToken');
    krt.Notification.registerFCMToken(fcmToken);
  }

  Future<void> setUserSyncScript(String? userSyncScript) async {
    if (userSyncScript != null) {
      try {
        mainWebViewController?.runJavaScriptReturningResult(userSyncScript);
      } catch (e) {
        debugPrint(e.toString());
      }
    }
  }

  Future<bool> isKartePush(RemoteMessage message) {
    return krt.Notification.canHandle(message);
  }

  void handleOpenUrl(String url) {
    if (url.isPdfUrl()) {
      if (MyApp.observer.isPdfOpening) {
        MyApp.observer.router?.replace(PdfViewerRoute(pdfUrl: url));
      } else {
        MyApp.observer.router?.push(PdfViewerRoute(pdfUrl: url));
      }
      return;
    }
    if (url.shouldExternalBrowser()) {
      launchUrlString(url);
      return;
    }
    clearPdfStack();
    try {
      mainWebViewController?.loadRequest(Uri.parse(url));
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void postNotifyKarteData(String? url) {
    if (url == null) return;
    if (mainWebViewController == null || isLoading) {
      _queuePostNotifyKarteData.add(() {
        KarteUtil().handleOpenUrl(url);
      });
    } else {
      debugPrint('_postNotifyKarteData $url');
      KarteUtil().handleOpenUrl(url);
    }
  }

  void executeQueuePostNotifyKarteData() {
    if (_queuePostNotifyKarteData.isNotEmpty) {
      _queuePostNotifyKarteData.last();
      _queuePostNotifyKarteData.clear();
    }
  }
}
