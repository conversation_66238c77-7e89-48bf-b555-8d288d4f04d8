import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/gen/assets.gen.dart';
import 'package:kc_member_site_native/themes/app_colors.dart';
import 'package:kc_member_site_native/themes/app_text_theme.dart';
import 'package:super_tooltip/super_tooltip.dart';

class TooltipUtils {
  static final TooltipUtils _singleton = TooltipUtils._internal();
  factory TooltipUtils() {
    return _singleton;
  }
  TooltipUtils._internal();

  SuperTooltip createTooltip(String message, [double? arrowTipDistance]) {
    return SuperTooltip(
      popupDirection: TooltipDirection.down,
      borderColor: AppColors.primaryColor,
      borderWidth: 1,
      borderRadius: 5,
      arrowLength: 6,
      arrowBaseWidth: 16,
      backgroundColor: AppColors.orange100Color,
      minimumOutSidePadding: 45.w,
      shadowBlurRadius: 8,
      shadowOffset: const Offset(2, 2),
      shadowColor: AppColors.dropShadowColor,
      shadowSpreadRadius: 1,
      outsideBackgroundColor: Colors.transparent,
      arrowTipDistance: arrowTipDistance ?? 7.w,
      content: Material(
          color: Colors.transparent,
          child: Padding(
            padding: EdgeInsets.all(3.w),
            child: Text(
              message,
              style: AppTextTheme.text500Style.copyWith(fontSize: 12.w),
              textAlign: TextAlign.center,
              softWrap: true,
            ),
          )),
    );
  }

  SuperTooltip _createToast(String message, [double? arrowTipDistance]) {
    return SuperTooltip(
      popupDirection: TooltipDirection.up,
      borderColor: AppColors.primaryColor,
      borderWidth: 1,
      borderRadius: 3,
      arrowLength: 0,
      arrowBaseWidth: 0,
      backgroundColor: AppColors.mainBgColor,
      minimumOutSidePadding: 15.w,
      shadows: [
        const BoxShadow(
          color: Colors.white,
          blurRadius: 5,
          spreadRadius: 1,
          offset: Offset(-3, -3),
        ),
        const BoxShadow(
          color: AppColors.dropShadowToast,
          blurRadius: 10,
          spreadRadius: 1,
          offset: Offset(3, 3),
        ),
      ],
      outsideBackgroundColor: Colors.transparent,
      arrowTipDistance: arrowTipDistance ?? 15.w,
      dismissOnTapOutside: false,
      blockOutsidePointerEvents: false,
      bubbleDimensions: EdgeInsets.zero,
      content: Material(
          color: AppColors.orange200Color,
          child: Padding(
            padding: EdgeInsets.all(15.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Assets.svgs.iconInfo.svg(),
                SizedBox(width: 4.w),
                Text(
                  message,
                  style: TextStyle(fontSize: 12.w, fontWeight: FontWeight.w500, letterSpacing: 0.36, color: AppColors.primaryColor),
                  textAlign: TextAlign.center,
                  softWrap: true,
                ),
              ],
            ),
          )),
    );
  }

  SuperTooltip? _toast;
  Timer? _timer;

  void _cancelToast() {
    _toast?.close();
    _timer?.cancel();
  }

  void showToast({
    required BuildContext toastContext,
    required String message,
    double? arrowTipDistance,
    VoidCallback? onClose,
  }) {
    _cancelToast();
    _toast = _createToast(message, arrowTipDistance);
    _toast?.show(toastContext);

    _timer = Timer(const Duration(milliseconds: 1500), () {
      _cancelToast();
      _toast = null;
      onClose?.call();
    });
  }
}
