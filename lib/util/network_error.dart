// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:chopper/chopper.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:kc_member_site_native/constant/base.dart';
import 'package:kc_member_site_native/extensions/build_context_extensions.dart';
import 'package:kc_member_site_native/model/login_model.dart';
import 'package:kc_member_site_native/routes/app_router.dart';

import 'app_dialog.dart';

Future<T>? checkConnectionError<T>(
  BuildContext context,
  ConnectionError? error, {
  Future<T> Function()? onRetry,
  bool isSplash = false,
}) async {
  Completer<T> result = Completer<T>();
  List<ActionProps> actions = [
    ActionProps(
        onPressed: (context) async {
          Navigator.pop(context);
          final res = await onRetry?.call();
          result.complete(res);
        },
        child: Text(context.l10n.retry))
  ];
  if (!isSplash) {
    actions.insert(
        0,
        ActionProps(
            onPressed: (context) {
              Navigator.pop(context);
              result.complete();
            },
            child: Text(context.l10n.close)));
  }
  switch (error?.type) {
    case ConnectionErrorType.requestTimeout:
      await showNetworkErrorTimeout(context, actions);
      break;
    case ConnectionErrorType.systemError:
      await showNetworkErrorSystem(context, actions);
      break;
    case ConnectionErrorType.maintenance:
      await showNetworkErrorMaintenance(context, actions);
      break;
    case ConnectionErrorType.unknownError:
    case ConnectionErrorType.networkError:
    default:
      await showNetworkErrorConnectivity(context, actions);
      break;
  }
  return result.future;
}

enum ConnectionErrorType {
  networkError,
  requestTimeout,
  systemError,
  maintenance,
  unknownError,
}

class ConnectionError {
  ConnectionError({this.type});
  final ConnectionErrorType? type;
}

class DataResult<T> {
  const DataResult({this.data, this.error});
  final T? data;
  final ConnectionError? error;
  bool get isError => error != null;
}

Future<DataResult<T>> guardRequest<T>(Future<T> Function() request) async {
  try {
    return DataResult(data: await request());
  } on TimeoutException {
    return DataResult(error: ConnectionError(type: ConnectionErrorType.requestTimeout));
  } catch (e) {
    if (e is SocketException) {
      if (e.osError != null) {
        return DataResult(error: ConnectionError(type: ConnectionErrorType.networkError));
      }
      return DataResult(error: ConnectionError(type: ConnectionErrorType.requestTimeout));
    }
    if (e is http.ClientException) {
      if (e.toString() == Base.timeoutExceptionMsg) {
        return DataResult(error: ConnectionError(type: ConnectionErrorType.requestTimeout));
      }
      return DataResult(error: ConnectionError(type: ConnectionErrorType.networkError));
    }
    if (e is Response) {
      switch (e.statusCode) {
        case 404:
        case 500:
          return DataResult(error: ConnectionError(type: ConnectionErrorType.systemError));
        case 503:
          return DataResult(error: ConnectionError(type: ConnectionErrorType.maintenance));
      }
    }
    return DataResult(error: ConnectionError(type: ConnectionErrorType.unknownError));
  }
}

Future<void> mfaErrorHandle({required LoginErrorModel? error, required BuildContext context, required VoidCallback onRetry}) async {
  if (error?.httpStatus == 401 ||
      [
        ErrorCode.transactionNotFound,
        ErrorCode.expiredToken,
      ].contains(error?.errorCode)) {
    await DialogUtils.showDl04(context, context.l10n.me11);
    context.replaceRoute(LoginRoute());
    return;
  }
  switch (error?.errorCode) {
    case ErrorCode.authPending:
      await Future.delayed(
        const Duration(seconds: 1),
        onRetry,
      );
      return;
    case ErrorCode.pollingTooFast:
      await Future.delayed(
        const Duration(seconds: 5),
        onRetry,
      );
      return;
    default:
      await DialogUtils.showDl04(context, context.l10n.me03);
      context.replaceRoute(LoginRoute());
      break;
  }
}
