import 'package:flutter/material.dart';
import 'package:kc_member_site_native/view_model/base_view_model.dart';
import 'package:provider/provider.dart';

mixin BaseStateMixin<S extends StatefulWidget, V extends BaseViewModel> on State<S> {
  V get vm => context.read<V>();

  void initData() {}

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      initData();
      vm.initData();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    vm.setCurrentContext(context);
  }
}

abstract class BaseState<S extends StatefulWidget, V extends BaseViewModel> extends State<S> with BaseStateMixin<S, V> {}
