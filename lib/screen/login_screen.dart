import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:flutter_neumorphic/flutter_neumorphic.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/constant/enum.dart';
import 'package:kc_member_site_native/gen/assets.gen.dart';
import 'package:kc_member_site_native/routes/app_router.dart';
import 'package:kc_member_site_native/screen/base_state.dart';
import 'package:kc_member_site_native/util/app_info.dart';
import 'package:kc_member_site_native/util/circular_indicator.dart';
import 'package:kc_member_site_native/util/pop_scope.dart';
import 'package:kc_member_site_native/view_model/login_view_model.dart';
import 'package:kc_member_site_native/widgets/kc_neumorphic_button.dart';
import 'package:kc_member_site_native/widgets/kc_switch.dart';
import 'package:kc_member_site_native/widgets/kc_text_field.dart';
import 'package:provider/provider.dart';

import '../constant/base.dart';
import '../util/in_app_browser.dart';

@RoutePage()
class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key, this.actionType}) : super(key: key);
  final ActionType? actionType;

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends BaseState<LoginScreen, LoginViewModel> {
  final _packageInfo = AppInfo().packageInfo;
  bool _isLoading = false;

  @override
  void initData() {
    super.initData();
    vm.isSplash = false;
    vm.getAnnounce();
    if (widget.actionType == ActionType.sessionInvalid && vm.isAutoLogin && vm.canLogin) {
      _onPressLogin(vm);
    }
  }

  Future<void> _onPressLogin(LoginViewModel model) async {
    FocusManager.instance.primaryFocus?.unfocus();
    setState(() {
      _isLoading = true;
    });
    await model.loginProgress();
    if (!mounted) return;
    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: PopScopeUtil().onWillPop,
      child: GestureDetector(
        onTap: () {
          // Hide soft input keyboard on clicking outside TextField
          FocusManager.instance.primaryFocus?.unfocus();
        },
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          body: Stack(
            children: [
              SafeArea(
                child: SingleChildScrollView(
                  physics: const ClampingScrollPhysics(),
                  child: _buildBody(),
                ),
              ),
              if (_isLoading)
                Container(
                  color: const Color.fromRGBO(255, 255, 255, 0.5),
                  alignment: Alignment.center,
                  child: circularIndicator(),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBody() {
    final maintenanceAnnounce = context.select<LoginViewModel, String?>((value) => value.maintenanceAnnounce);
    final hasMaintenanceAnnounce = maintenanceAnnounce != null && maintenanceAnnounce.isNotEmpty;

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          height: hasMaintenanceAnnounce ? 124.h : 247.h,
          child: Assets.images.mufgLogo.image(
            fit: BoxFit.contain,
            width: 252.w,
          ),
        ),
        if (hasMaintenanceAnnounce) ...[
          _buildMaintenanceAnnounce(maintenanceAnnounce),
          SizedBox(height: 43.h),
        ],
        _buildLoginForms(),
        Padding(padding: EdgeInsets.only(top: 65.h)),
        SizedBox(
          height: 36.h,
          child: TextButton(
              style: TextButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 10.w),
              ),
              onPressed: () => launchUrlPlatform(context, Base.troubleLoggingInUrl),
              child: Text('ログインにお困りの方はこちら',
                  style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500, color: Theme.of(context).primaryColor))),
        ),
        Padding(padding: EdgeInsets.only(top: 21.h)),
        Divider(
          height: 1.h,
          thickness: 1.h,
          color: const Color.fromRGBO(0xd1, 0xd1, 0xd6, 1),
          indent: 18.w,
          endIndent: 18.w,
        ),
        Padding(padding: EdgeInsets.only(top: 30.h)),
        _buildCreateAccount(),
        Padding(padding: EdgeInsets.only(top: 31.h)),
        _buildVersion(),
      ],
    );
  }

  Widget _buildMaintenanceAnnounce(String message) {
    return Container(
      width: double.infinity,
      constraints: BoxConstraints(minHeight: 80.h),
      decoration: BoxDecoration(
        color: const Color(0xffFFFFD1),
        border: Border.all(color: const Color(0xffBB271A)),
      ),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      margin: EdgeInsets.symmetric(horizontal: 15.w),
      child: Text(
        message,
        style: TextStyle(fontSize: 14.sp, color: const Color(0xffBB271A)),
      ),
    );
  }

  Widget _buildLoginForms() {
    return Consumer<LoginViewModel>(builder: ((context, model, child) {
      return Padding(
        padding: EdgeInsets.only(left: 15.w, right: 15.w),
        child: Column(
          children: [
            Row(
              children: [
                Flexible(
                  child: KCTextField(
                    initialValue: model.username,
                    placeholder: "口座番号を入力",
                    keyboardType: TextInputType.number,
                    onChanged: model.onChangeUsername,
                  ),
                ),
                SizedBox(width: 32.w),
                KCSwitch(
                  title: "保存",
                  value: model.isSaveUsername,
                  onChanged: model.toggleUsername,
                  isDisable: model.isAutoLogin,
                ),
              ],
            ),
            SizedBox(height: 20.h),
            Row(
              children: [
                Flexible(
                  child: KCTextField(
                    initialValue: model.password,
                    placeholder: "パスワードを入力",
                    keyboardType: TextInputType.text,
                    obscureText: true,
                    onChanged: model.onChangePassword,
                  ),
                ),
                SizedBox(width: 32.w),
                KCSwitch(
                  title: "保存",
                  value: model.isSavePassword,
                  onChanged: model.togglePassword,
                  isDisable: model.isAutoLogin,
                ),
              ],
            ),
            SizedBox(height: 33.h),
            KCSwitch(
              title: "自動ログイン",
              value: model.isAutoLogin,
              onChanged: model.toggleAutoLogin,
            ),
            SizedBox(
              height: 42.h,
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 30.w),
              height: 50.h,
              child: KcNeumorphicButton(
                padding: EdgeInsets.zero,
                style: NeumorphicStyle(
                    color: model.canLogin ? Theme.of(context).primaryColor : const Color.fromRGBO(0xcc, 0xcc, 0xcc, 1),
                    boxShape: const NeumorphicBoxShape.stadium()),
                onPressed: model.canLogin ? () => _onPressLogin(model) : null,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text('ログイン', style: TextStyle(color: Colors.white, fontSize: 18.sp, fontWeight: FontWeight.bold)),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }));
  }

  Widget _buildVersion() {
    return SizedBox(
      height: 36.h,
      child: TextButton(
        onPressed: () {
          context.router.push(LicenseRoute(
            applicationName: _packageInfo.appName,
            applicationVersion: _packageInfo.version,
          ));
        },
        style: TextButton.styleFrom(
          padding: EdgeInsets.symmetric(horizontal: 10.w),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('現在のバージョン',
                style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500, color: const Color.fromRGBO(0x48, 0x48, 0x48, 1))),
            SizedBox(
              width: 17.w,
            ),
            Text(_packageInfo.version,
                style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500, color: const Color.fromRGBO(0x48, 0x48, 0x48, 1))),
          ],
        ),
      ),
    );
  }

  Widget _buildCreateAccount() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 60.w),
      height: 36.h,
      child: NeumorphicButton(
        padding: EdgeInsets.zero,
        style: NeumorphicStyle(
          boxShape: const NeumorphicBoxShape.stadium(),
          border: NeumorphicBorder(isEnabled: true, color: Theme.of(context).primaryColor, width: 1),
          color: const Color.fromRGBO(0xf4, 0xf4, 0xf4, 1),
        ),
        onPressed: () => launchUrlPlatform(context, Base.createAccountUrl),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text('口座を開設する', style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor)),
          ],
        ),
      ),
    );
  }
}
