// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_jailbreak_detection/flutter_jailbreak_detection.dart';
import 'package:flutter_neumorphic/flutter_neumorphic.dart';
import 'package:kc_member_site_native/constant/app_config.dart';
import 'package:kc_member_site_native/constant/base.dart';
import 'package:kc_member_site_native/extensions/build_context_extensions.dart';
import 'package:kc_member_site_native/gen/assets.gen.dart';
import 'package:kc_member_site_native/model/check_version_model.dart';
import 'package:kc_member_site_native/routes/app_router.dart';
import 'package:kc_member_site_native/screen/base_state.dart';
import 'package:kc_member_site_native/service/app_version_service.dart';
import 'package:kc_member_site_native/service/banhmi_service.dart';
import 'package:kc_member_site_native/util/app_dialog.dart';
import 'package:kc_member_site_native/util/app_info.dart';
import 'package:kc_member_site_native/util/circular_indicator.dart';
import 'package:kc_member_site_native/util/in_app_browser.dart';
import 'package:kc_member_site_native/util/network_error.dart';
import 'package:kc_member_site_native/util/pop_scope.dart';
import 'package:kc_member_site_native/view_model/login_view_model.dart';
import 'package:provider/provider.dart';
import 'package:webview_flutter/webview_flutter.dart';

import 'webview_screen.dart';

@RoutePage()
class InitialScreen extends StatefulWidget {
  const InitialScreen({Key? key}) : super(key: key);

  @override
  State<InitialScreen> createState() => InitialScreenState();
}

class InitialScreenState extends BaseState<InitialScreen, LoginViewModel> {
  late AppVersionService _appVersionService;
  late BanhmiService _bmService;
  late Future<void> _checkJailbreak;
  late final WebViewController _controller;

  @override
  void initState() {
    _appVersionService = context.read<AppVersionService>();
    _bmService = context.read<BanhmiService>();
    _checkJailbreak = checkJailbreak();
    super.initState();
    _controller = WebViewController()
      ..setBackgroundColor(Colors.transparent)
      ..setJavaScriptMode(JavaScriptMode.unrestricted);
    _getUserAgent();
  }

  Future<void> _getUserAgent() async {
    final info = AppInfo().packageInfo;

    try {
      // On Android returns the evaluation result as a JSON formatted string.
      final userAgent = await _controller.runJavaScriptReturningResult("navigator.userAgent;") as String?;
      appUserAgent = '${Platform.isAndroid ? json.decode(userAgent ?? "") : userAgent}${Base.defaultUserAgent}${info.version}';
      debugPrint('UserAgent: $appUserAgent');
    } catch (e) {
      appUserAgent = '${Base.defaultUserAgent}${info.version}';
      debugPrint('get UserAgent fail: $appUserAgent');
    }
  }

  @override
  void initData() {
    super.initData();
    vm.isSplash = true;
    _checkJailbreak.then((_) async {
      final geo = await _bmService.getGeoLocation();
      if (!mounted) return;
      if (geo?.fromJapan == false) {
        await DialogUtils.showDl00(context, context.l10n.me00Geo);
        return;
      }
      _checkAppVersion(context);
    });
  }

  Future<void> checkJailbreak() async {
    bool isJailbreak;
    // Platform messages may fail, so we use a try/catch PlatformException.
    try {
      isJailbreak = await FlutterJailbreakDetection.jailbroken;
    } on PlatformException {
      isJailbreak = true;
    }
    if (!mounted) return;
    if (isJailbreak) {
      await showJailbreakDetect(context, [
        ActionProps(
            onPressed: (_) {
              if (Platform.isAndroid) {
                exit(0);
              }
            },
            child: Text(context.l10n.ok))
      ]);
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: PopScopeUtil().onWillPop,
      canPop: false,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        body: Stack(fit: StackFit.expand, children: [
          Center(
            child: Assets.images.mufgLogo.image(
              width: 252,
              fit: BoxFit.fitWidth,
            ),
          ),
          Container(alignment: Alignment.center, child: circularIndicator()),
        ]),
      ),
    );
  }

  void _openAppStore() {
    launchUrlString(Platform.isIOS ? Base.iOSStoreUrl : Base.androidStoreUrl);
  }

  Future<void> _checkAppVersion(BuildContext context) async {
    debugPrint("___start checkAppVersion");
    final checkVersion = await guardRequest(_appVersionService.checkVersion);
    if (checkVersion.isError) {
      await checkConnectionError<void>(
        context,
        checkVersion.error!,
        onRetry: () => _checkAppVersion(context),
        isSplash: true,
      );
      return;
    }
    final appVersionModel = checkVersion.data;
    debugPrint(appVersionModel.toString());
    if (appVersionModel?.updateType == UpdateType.imperative.name) {
      if (!mounted) return;
      await showMessageActionDialog(context, appVersionModel?.updateMessage ?? "", [
        ActionProps(onPressed: (_) => _openAppStore(), child: Text(context.l10n.update)),
      ]);
      return;
    } else if (appVersionModel?.updateType == UpdateType.voluntarily.name) {
      if (!mounted) return;
      await showMessageActionDialog(context, appVersionModel?.updateMessage ?? "", [
        ActionProps(onPressed: (context) => Navigator.pop(context), child: Text(context.l10n.later)),
        ActionProps(onPressed: (_) => _openAppStore(), child: Text(context.l10n.update)),
      ]);
    }
    initAppConfigJson();
    getAppConfig();
    if (!mounted) return;
    final loginVm = context.read<LoginViewModel>();
    if (loginVm.isAutoLogin) {
      loginVm.loginProgress();
      return;
    }
    context.router.replace(LoginRoute());
  }
}
