import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/extensions/build_context_extensions.dart';
import 'package:kc_member_site_native/gen/assets.gen.dart';
import 'package:kc_member_site_native/model/mfa_type.dart';
import 'package:kc_member_site_native/routes/app_router.dart';
import 'package:kc_member_site_native/screen/base_state.dart';
import 'package:kc_member_site_native/service/login_service.dart';
import 'package:kc_member_site_native/themes/app_colors.dart';
import 'package:kc_member_site_native/themes/app_text_theme.dart';
import 'package:kc_member_site_native/util/pop_scope.dart';
import 'package:kc_member_site_native/view_model/mfa/mfa_select_view_model.dart';
import 'package:kc_member_site_native/widgets/kc_app_bar.dart';
import 'package:kc_member_site_native/widgets/shadow_button.dart';
import 'package:provider/provider.dart';

final _boxWidth = 90.w;

@RoutePage()
class MfaSelectScreen extends StatefulWidget implements AutoRouteWrapper {
  const MfaSelectScreen({super.key});

  @override
  State<MfaSelectScreen> createState() => _MfaSelectScreenState();

  @override
  Widget wrappedRoute(BuildContext context) {
    return ChangeNotifierProvider(
      create: (ctx) => MfaSelectViewModel(
        loginService: context.read<LoginService>(),
      ),
      child: this,
    );
  }
}

class _MfaSelectScreenState extends BaseState<MfaSelectScreen, MfaSelectViewModel> {
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: PopScopeUtil().onWillPop,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: KcAppBar(titleText: context.l10n.chooseAuthMethod),
        body: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    final mfaType = context.select<MfaSelectViewModel, MfaType>((value) => value.mfaType);
    final spacingWidget = SizedBox(width: 11.w);

    return SafeArea(
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 62.w),
            Text(
              context.l10n.pleaseSelectAuthMethod,
              textAlign: TextAlign.center,
              style: AppTextTheme.text500Style,
            ),
            SizedBox(height: 31.w),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (mfaType.isMfaPush) ...[
                  spacingWidget,
                  _iconTitle(
                    icon: SizedBox(
                      width: _boxWidth,
                      height: _boxWidth,
                      child: Assets.images.mfaApp.image(),
                    ),
                    title: context.l10n.authWithNotificationApp,
                    onPressed: () {
                      context.replaceRoute(const MfaPushRoute());
                    },
                  ),
                  spacingWidget,
                ],
                if (mfaType.isMfaEmail) ...[
                  spacingWidget,
                  _iconTitle(
                    icon: Container(
                      padding: EdgeInsets.symmetric(horizontal: 14.w),
                      height: _boxWidth,
                      width: _boxWidth,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppColors.borderColor,
                        ),
                        borderRadius: const BorderRadius.all(Radius.circular(10)),
                      ),
                      child: Assets.svgs.mail.svg(),
                    ),
                    title: context.l10n.verifyCodeFromEmail,
                    onPressed: () {
                      context.replaceRoute(const MfaEmailRoute());
                    },
                  ),
                  spacingWidget,
                ],
                if (mfaType.isMfaRecovery) ...[
                  spacingWidget,
                  _iconTitle(
                    icon: Container(
                      padding: EdgeInsets.symmetric(horizontal: 14.w),
                      height: _boxWidth,
                      width: _boxWidth,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppColors.borderColor,
                        ),
                        borderRadius: const BorderRadius.all(Radius.circular(10)),
                      ),
                      child: Assets.svgs.recovery.svg(),
                    ),
                    title: context.l10n.authWithRecoveryCode,
                    onPressed: () {
                      context.replaceRoute(const MfaRecoveryInputRoute());
                    },
                  ),
                  spacingWidget,
                ],
              ],
            ),
            SizedBox(height: 35.w),
            SizedBox(
              width: 285.w,
              height: 35.w,
              child: ShadowStadiumButton(
                text: context.l10n.returnToLogin,
                onPressed: () {
                  context.replaceRoute(LoginRoute());
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _iconTitle({required String title, required Widget icon, required void Function()? onPressed}) {
    return TextButton(
      onPressed: onPressed,
      style: TextButton.styleFrom(
        padding: EdgeInsets.zero,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
      child: Column(
        children: [
          icon,
          SizedBox(height: 10.w),
          SizedBox(
            width: _boxWidth,
            child: Text(
              title,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 12.w, fontWeight: FontWeight.w500, color: AppColors.textColor),
            ),
          ),
        ],
      ),
    );
  }
}
