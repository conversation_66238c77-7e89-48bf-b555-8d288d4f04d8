import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/constant/enum.dart';
import 'package:kc_member_site_native/extensions/build_context_extensions.dart';
import 'package:kc_member_site_native/extensions/string_extensions.dart';
import 'package:kc_member_site_native/screen/base_state.dart';
import 'package:kc_member_site_native/service/login_service.dart';
import 'package:kc_member_site_native/themes/app_colors.dart';
import 'package:kc_member_site_native/themes/app_text_theme.dart';
import 'package:kc_member_site_native/util/pop_scope.dart';
import 'package:kc_member_site_native/view_model/mfa/mfa_totp_view_model.dart';
import 'package:kc_member_site_native/widgets/kc_app_bar.dart';
import 'package:kc_member_site_native/widgets/kc_pin_code_widget.dart';
import 'package:kc_member_site_native/widgets/loading/during_verify_widget.dart';
import 'package:kc_member_site_native/widgets/shadow_button.dart';
import 'package:provider/provider.dart';

@RoutePage()
class MfaEmailScreen extends StatefulWidget implements AutoRouteWrapper {
  const MfaEmailScreen({super.key});

  @override
  Widget wrappedRoute(BuildContext context) {
    final loginService = context.read<LoginService>();

    return ChangeNotifierProvider(
      create: (ctx) => MfaTotpViewModel(
        loginService: loginService,
        challengeType: ChallengeType.oob,
      ),
      child: this,
    );
  }

  @override
  State<MfaEmailScreen> createState() => _MfaEmailScreenState();
}

class _MfaEmailScreenState extends BaseState<MfaEmailScreen, MfaTotpViewModel> {
  final _textEditingController = TextEditingController();
  final _focusNode = FocusNode();
  late BuildContext _toastContext;

  @override
  Widget build(BuildContext context) {
    final isVerifying = context.select<MfaTotpViewModel, bool>((value) => value.isVerifying);

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: PopScopeUtil().onWillPop,
      child: GestureDetector(
        onTap: () {
          // Hide soft input keyboard on clicking outside TextField
          _focusNode.unfocus();
        },
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          appBar: KcAppBar(titleText: context.l10n.mfaEmailTitle, height: 60.w),
          body: SafeArea(
            child: SingleChildScrollView(
              child: AbsorbPointer(
                absorbing: isVerifying,
                child: _buildBody(isVerifying),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBody(bool isVerifying) {
    final isBusy = context.select<MfaTotpViewModel, bool>((value) => value.isBusy);
    final email = context.select<MfaTotpViewModel, String>((value) => value.email);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 41.w),
        Builder(builder: (BuildContext context) {
          _toastContext = context;
          return const SizedBox(height: 0);
        }),
        Text(
          context.l10n.pleaseEnterVerifyCode,
          textAlign: TextAlign.center,
          style: AppTextTheme.text500Style,
        ),
        SizedBox(height: 15.w),
        Text(
          context.l10n.sendCodeToFollowingEmail,
          textAlign: TextAlign.center,
          style: AppTextTheme.text500Style,
        ),
        SizedBox(height: 15.w),
        Container(
          width: double.infinity,
          margin: EdgeInsets.symmetric(horizontal: 45.w),
          padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 5.w),
          decoration: BoxDecoration(
            border: Border.all(
              color: AppColors.gray200Color,
            ),
            borderRadius: const BorderRadius.all(Radius.circular(3)),
          ),
          child: Text(
            email.useCorrectEllipsis(),
            style: AppTextTheme.textNumberStyle.copyWith(fontSize: 14.w, fontWeight: FontWeight.w400, height: 1.3),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        SizedBox(height: 30.w),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 15.w),
          child: KcPinCodeWidget(
            focusNode: _focusNode,
            textEditingController: _textEditingController,
            onCompleted: (value) => vm.onCompletedEnterOtp(
              otp: value,
              teController: _textEditingController,
            ),
          ),
        ),
        Builder(builder: (BuildContext context) {
          vm.setTooltipContext(context);
          return const SizedBox(height: 0);
        }),
        SizedBox(
          height: 100.w,
          child: Column(
            children: [
              SizedBox(height: 30.w),
              isVerifying ? const DuringVerifyWidget() : const SizedBox.shrink(),
            ],
          ),
        ),
        SizedBox(
          width: 285.w,
          height: 35.w,
          child: ShadowStadiumButton(
            text: context.l10n.resendCode,
            onPressed: isBusy ? null : () => vm.onPressResendCode(_toastContext),
          ),
        ),
        SizedBox(height: 30.w),
        SizedBox(
          width: 285.w,
          height: 35.w,
          child: ShadowStadiumButton(
            text: context.l10n.authAnotherMethod,
            onPressed: () => vm.authAnotherMethod(),
          ),
        ),
      ],
    );
  }
}
