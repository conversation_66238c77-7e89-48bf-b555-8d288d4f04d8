import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/constant/enum.dart';
import 'package:kc_member_site_native/extensions/build_context_extensions.dart';
import 'package:kc_member_site_native/screen/base_state.dart';
import 'package:kc_member_site_native/service/login_service.dart';
import 'package:kc_member_site_native/themes/app_text_theme.dart';
import 'package:kc_member_site_native/util/pop_scope.dart';
import 'package:kc_member_site_native/view_model/mfa/mfa_totp_view_model.dart';
import 'package:kc_member_site_native/widgets/kc_app_bar.dart';
import 'package:kc_member_site_native/widgets/kc_pin_code_widget.dart';
import 'package:kc_member_site_native/widgets/loading/during_verify_widget.dart';
import 'package:kc_member_site_native/widgets/shadow_button.dart';
import 'package:provider/provider.dart';

@RoutePage()
class MfaTotpScreen extends StatefulWidget implements AutoRouteWrapper {
  const MfaTotpScreen({super.key});

  @override
  Widget wrappedRoute(BuildContext context) {
    final loginService = context.read<LoginService>();

    return ChangeNotifierProvider(
      create: (ctx) => MfaTotpViewModel(
        loginService: loginService,
        challengeType: ChallengeType.otp,
      ),
      child: this,
    );
  }

  @override
  State<MfaTotpScreen> createState() => _MfaTotpScreenState();
}

class _MfaTotpScreenState extends BaseState<MfaTotpScreen, MfaTotpViewModel> {
  final _textEditingController = TextEditingController();
  final _focusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    final isVerifying = context.select<MfaTotpViewModel, bool>((value) => value.isVerifying);

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: PopScopeUtil().onWillPop,
      child: GestureDetector(
        onTap: () {
          // Hide soft input keyboard on clicking outside TextField
          _focusNode.unfocus();
        },
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          appBar: KcAppBar(titleText: context.l10n.mfaTotpTitle, height: 60.w),
          body: SafeArea(
            child: SingleChildScrollView(
              child: AbsorbPointer(
                absorbing: isVerifying,
                child: _buildBody(isVerifying),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBody(bool isVerifying) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 41.w),
        Text(
          context.l10n.pleaseEnterVerifyCode,
          textAlign: TextAlign.center,
          style: AppTextTheme.text500Style,
        ),
        SizedBox(height: 30.w),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 15.w),
          child: KcPinCodeWidget(
            focusNode: _focusNode,
            textEditingController: _textEditingController,
            onCompleted: (value) => vm.onCompletedEnterOtp(
              otp: value,
              teController: _textEditingController,
            ),
          ),
        ),
        Builder(builder: (BuildContext context) {
          vm.setTooltipContext(context);
          return const SizedBox(height: 0);
        }),
        SizedBox(
          height: 100.w,
          child: Column(
            children: [
              SizedBox(height: 30.w),
              isVerifying ? const DuringVerifyWidget() : const SizedBox.shrink(),
            ],
          ),
        ),
        SizedBox(
          width: 285.w,
          height: 35.w,
          child: ShadowStadiumButton(
            text: context.l10n.authAnotherMethod,
            onPressed: () => vm.authAnotherMethod(),
          ),
        ),
      ],
    );
  }
}
