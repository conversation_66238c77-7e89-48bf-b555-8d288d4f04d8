import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/extensions/build_context_extensions.dart';
import 'package:kc_member_site_native/extensions/string_extensions.dart';
import 'package:kc_member_site_native/screen/base_state.dart';
import 'package:kc_member_site_native/service/login_service.dart';
import 'package:kc_member_site_native/service/polling_service.dart';
import 'package:kc_member_site_native/themes/app_colors.dart';
import 'package:kc_member_site_native/themes/app_text_theme.dart';
import 'package:kc_member_site_native/util/pop_scope.dart';
import 'package:kc_member_site_native/view_model/mfa/mfa_mail_verify_view_model.dart';
import 'package:kc_member_site_native/widgets/kc_link_button.dart';
import 'package:kc_member_site_native/widgets/shadow_button.dart';
import 'package:provider/provider.dart';

@RoutePage()
class MfaMailVerifyScreen extends StatefulWidget implements AutoRouteWrapper {
  const MfaMailVerifyScreen({
    super.key,
    required this.email,
  });
  final String email;

  @override
  Widget wrappedRoute(BuildContext context) {
    return ChangeNotifierProvider(
      create: (ctx) => MfaMailVerifyViewModel(
        loginService: context.read<LoginService>(),
        pollingService: context.read<PollingService>(),
      ),
      child: this,
    );
  }

  @override
  State<MfaMailVerifyScreen> createState() => _MfaMailVerifyScreenState();
}

class _MfaMailVerifyScreenState extends BaseState<MfaMailVerifyScreen, MfaMailVerifyViewModel> {
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: PopScopeUtil().onWillPop,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        body: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: _buildBody(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBody() {
    final bool hasResendEmail = context.select<MfaMailVerifyViewModel, bool>((value) => value.hasResendEmail);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 60.w),
        Text(
          context.l10n.emailVerifyTitle,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16.w,
            fontWeight: FontWeight.bold,
            color: AppColors.gray500Color,
            height: AppTextTheme.height24_16,
          ),
        ),
        SizedBox(height: 30.w),
        Text(
          context.l10n.emailHasBeenSentToTheAddressBelow,
          textAlign: TextAlign.center,
          style: AppTextTheme.text500Style.copyWith(
            height: AppTextTheme.height18_14,
          ),
        ),
        SizedBox(height: 30.w),
        Container(
          width: double.infinity,
          margin: EdgeInsets.symmetric(horizontal: 30.w),
          padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 5.w),
          decoration: BoxDecoration(
            border: Border.all(
              color: AppColors.gray200Color,
            ),
            borderRadius: const BorderRadius.all(Radius.circular(3)),
          ),
          child: Text(
            widget.email.useCorrectEllipsis(),
            style: AppTextTheme.textNumberStyle.copyWith(fontSize: 14.w, fontWeight: FontWeight.w400, height: 1.3),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        SizedBox(height: 30.w),
        Text.rich(
          style: AppTextTheme.text500Style.copyWith(
            height: AppTextTheme.height18_14,
          ),
          TextSpan(
            text: context.l10n.completeEmailAddressByClickLink,
            children: [
              WidgetSpan(
                child: InkWell(
                  borderRadius: BorderRadius.circular(3),
                  onTap: () => vm.nextScreenDeterminationProcess(),
                  child: Text(
                    context.l10n.here,
                    style: AppTextTheme.textLinkStyle.copyWith(
                      height: AppTextTheme.height18_14,
                      fontSize: 14.w,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 48.w),
        if (!hasResendEmail)
          SizedBox(
            width: 255.w,
            height: 35.w,
            child: ShadowStadiumButton(
              text: context.l10n.resendConfirmationEmail,
              onPressed: () => vm.mfaEmailResend(),
            ),
          ),
        if (hasResendEmail)
          Text(
            context.l10n.requestedConfirmEmailResent,
            style: AppTextTheme.text500Style.copyWith(
              height: AppTextTheme.height18_14,
            ),
          ),
        SizedBox(height: 30.w),
        SizedBox(
          width: 255.w,
          height: 35.w,
          child: ShadowStadiumButton(
            text: context.l10n.checkLater,
            onPressed: () => vm.nextScreenDeterminationProcess(),
          ),
        ),
        SizedBox(height: 30.w),
        Text(
          context.l10n.changeYourRegisteredEmail,
          style: AppTextTheme.text500Style.copyWith(
            height: AppTextTheme.height18_14,
          ),
        ),
        SizedBox(height: 18.w),
        _buildLinkButton(
          context,
          text: context.l10n.howToChangeYourAuthEmail,
          onTap: () => vm.onTapHowToChangeYourAuthEmail(),
        ),
        SizedBox(height: 18.w),
        _buildLinkButton(
          context,
          text: context.l10n.whatHappenIfNoVerifyEmail,
          onTap: () => vm.onTapWhatHappenIfNoVerifyEmail(),
        ),
        SizedBox(height: 42.w),
      ],
    );
  }

  Widget _buildLinkButton(
    BuildContext context, {
    required String text,
    void Function()? onTap,
  }) {
    return Align(
      alignment: Alignment.centerLeft,
      child: KcLinkButton(
        onTap: onTap,
        text: text,
      ),
    );
  }
}
