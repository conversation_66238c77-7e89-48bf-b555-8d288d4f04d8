import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/extensions/build_context_extensions.dart';
import 'package:kc_member_site_native/screen/base_state.dart';
import 'package:kc_member_site_native/service/login_service.dart';
import 'package:kc_member_site_native/service/secure_storage_service.dart';
import 'package:kc_member_site_native/themes/app_colors.dart';
import 'package:kc_member_site_native/themes/app_text_theme.dart';
import 'package:kc_member_site_native/util/pop_scope.dart';
import 'package:kc_member_site_native/view_model/mfa/mfa_push_view_model.dart';
import 'package:kc_member_site_native/widgets/kc_dropdown.dart';
import 'package:kc_member_site_native/widgets/loading/kc_fading_loading.dart';
import 'package:kc_member_site_native/widgets/shadow_button.dart';
import 'package:provider/provider.dart';

@RoutePage()
class MfaPushScreen extends StatefulWidget implements AutoRouteWrapper {
  const MfaPushScreen({super.key});

  @override
  Widget wrappedRoute(BuildContext context) {
    final loginService = context.read<LoginService>();
    final secureStorage = context.read<SecureStorageService>();
    return ChangeNotifierProvider(
      create: (ctx) => MfaPushViewModel(loginService: loginService, secureStorage: secureStorage),
      child: this,
    );
  }

  @override
  State<MfaPushScreen> createState() => _MfaPushScreenState();
}

class _MfaPushScreenState extends BaseState<MfaPushScreen, MfaPushViewModel> {
  late BuildContext _toastContext;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: PopScopeUtil().onWillPop,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        body: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    final isBusy = context.select<MfaPushViewModel, bool>((value) => value.isBusy);

    return SafeArea(
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 100.w),
            Builder(builder: (BuildContext context) {
              _toastContext = context;
              return const SizedBox(height: 0);
            }),
            KcFadingLoading(
              color: AppColors.gray200Color,
              radius: 51.w,
              lineWidth: 20,
              rx: 2.8,
            ),
            SizedBox(height: 50.w),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Text(
                context.l10n.pleaseAllowNotice,
                textAlign: TextAlign.center,
                style: AppTextTheme.text500Style,
              ),
            ),
            SizedBox(height: 15.w),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 45.w),
              child: Consumer<MfaPushViewModel>(builder: (context, vm, _) {
                return KcDropdown(
                  isEnable: !isBusy,
                  options: vm.deviceList,
                  initialValue: vm.deviceSelected,
                  onChange: (value) => vm.onDeviceSelected(value),
                );
              }),
            ),
            SizedBox(height: 35.w),
            SizedBox(
              width: 285.w,
              height: 35.w,
              child: ShadowStadiumButton(
                text: context.l10n.resendNotification,
                onPressed: isBusy ? null : () => vm.resendNotification(_toastContext),
              ),
            ),
            SizedBox(height: 30.w),
            SizedBox(
              width: 285.w,
              height: 35.w,
              child: ShadowStadiumButton(
                text: context.l10n.authWithCode,
                onPressed: () => vm.authWithCode(),
              ),
            ),
            SizedBox(height: 30.w),
            SizedBox(
              width: 285.w,
              height: 35.w,
              child: ShadowStadiumButton(
                text: context.l10n.authAnotherMethod,
                onPressed: () => vm.authAnotherMethod(),
              ),
            ),
            SizedBox(height: 30.w),
          ],
        ),
      ),
    );
  }
}
