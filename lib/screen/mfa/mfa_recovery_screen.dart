import 'package:auto_route/auto_route.dart';
import 'package:flutter/services.dart';
import 'package:flutter_neumorphic/flutter_neumorphic.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/extensions/build_context_extensions.dart';
import 'package:kc_member_site_native/model/login_model.dart';
import 'package:kc_member_site_native/themes/app_colors.dart';
import 'package:kc_member_site_native/themes/app_text_theme.dart';
import 'package:kc_member_site_native/util/app_tooltip.dart';
import 'package:kc_member_site_native/util/pop_scope.dart';
import 'package:kc_member_site_native/view_model/login_view_model.dart';
import 'package:kc_member_site_native/widgets/kc_app_bar.dart';
import 'package:kc_member_site_native/widgets/kc_checkbox.dart';
import 'package:kc_member_site_native/widgets/shadow_button.dart';
import 'package:provider/provider.dart';

@RoutePage()
class MfaRecoveryScreen extends StatefulWidget {
  final String newRecoveryCode;
  final LoginModel mfaOauthToken;
  const MfaRecoveryScreen({super.key, required this.newRecoveryCode, required this.mfaOauthToken});

  @override
  State<MfaRecoveryScreen> createState() => _MfaRecoveryScreenState();
}

class _MfaRecoveryScreenState extends State<MfaRecoveryScreen> {
  late BuildContext _toastContext;
  bool _isBusy = false;
  bool _codeRecordCheck = false;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: PopScopeUtil().onWillPop,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: KcAppBar(
          titleText: context.l10n.recoveryCode,
          height: 55.w,
        ),
        body: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    return SafeArea(
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 44.w),
            Builder(builder: (BuildContext context) {
              _toastContext = context;
              return const SizedBox(height: 0);
            }),
            Text(
              context.l10n.pleaseKeepItInSafePlace,
              textAlign: TextAlign.center,
              style: AppTextTheme.text500Style,
            ),
            SizedBox(height: 30.w),
            Container(
              width: double.infinity,
              constraints: BoxConstraints(
                minHeight: 80.w,
              ),
              margin: EdgeInsets.symmetric(horizontal: 15.w),
              child: ShadowStadiumButton(
                padding: const EdgeInsets.all(10),
                style: neumorphicBaseStyle.copyWith(
                  intensity: 0.75,
                  boxShape: NeumorphicBoxShape.roundRect(BorderRadius.all(Radius.circular(6.w))),
                  color: AppColors.gray10Color,
                ),
                child: Row(mainAxisAlignment: MainAxisAlignment.center, crossAxisAlignment: CrossAxisAlignment.center, children: [
                  Flexible(
                    child: Text(
                      widget.newRecoveryCode,
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                      style: AppTextTheme.text500Style.copyWith(fontWeight: FontWeight.bold),
                    ),
                  ),
                ]),
              ),
            ),
            SizedBox(height: 50.w),
            SizedBox(
              width: 285.w,
              height: 35.w,
              child: ShadowStadiumButton(
                text: context.l10n.copyToClipboard,
                onPressed: () {
                  Clipboard.setData(ClipboardData(text: widget.newRecoveryCode)).then((_) {
                    if(!mounted) return;
                    TooltipUtils().showToast(
                      toastContext: _toastContext,
                      message: context.l10n.copiedToClipboard,
                      arrowTipDistance: 25.w,
                    );
                  });
                },
                textStyle: TextStyle(
                    fontSize: 12.w, fontWeight: FontWeight.bold, color: AppColors.primaryColor, height: 1.25, letterSpacing: 0.36),
                style: neumorphicBaseStyle.copyWith(
                  boxShape: const NeumorphicBoxShape.stadium(),
                  border: const NeumorphicBorder(color: AppColors.primaryColor, width: 1),
                ),
              ),
            ),
            SizedBox(height: 40.w),
            KcCheckbox(
              title: context.l10n.safelyRecordedYourCode,
              value: _codeRecordCheck,
              onChanged: (value) {
                setState(() {
                  _codeRecordCheck = value;
                });
              },
              margin: EdgeInsets.only(right: 15.w),
            ),
            SizedBox(height: 44.w),
            SizedBox(
              width: 285.w,
              height: 50.w,
              child: ShadowStadiumButton(
                text: context.l10n.continueText,
                bgColor: _codeRecordCheck ? AppColors.primaryColor : AppColors.disableBgColor,
                textStyle: TextStyle(fontSize: 18.w, fontWeight: FontWeight.bold, color: Colors.white, height: 1.25),
                onPressed: _isBusy || !_codeRecordCheck
                    ? null
                    : () async {
                        setState(() {
                          _isBusy = true;
                        });
                        await context.read<LoginViewModel>().postLoginProcessing(context, widget.mfaOauthToken);
                        if (mounted) {
                          setState(() {
                            _isBusy = false;
                          });
                        }
                      },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
