import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter_neumorphic/flutter_neumorphic.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/extensions/build_context_extensions.dart';
import 'package:kc_member_site_native/screen/base_state.dart';
import 'package:kc_member_site_native/service/login_service.dart';
import 'package:kc_member_site_native/themes/app_colors.dart';
import 'package:kc_member_site_native/themes/app_text_theme.dart';
import 'package:kc_member_site_native/util/pop_scope.dart';
import 'package:kc_member_site_native/view_model/mfa/mfa_recovery_view_model.dart';
import 'package:kc_member_site_native/widgets/kc_app_bar.dart';
import 'package:kc_member_site_native/widgets/kc_link_button.dart';
import 'package:kc_member_site_native/widgets/loading/during_verify_widget.dart';
import 'package:kc_member_site_native/widgets/shadow_button.dart';
import 'package:provider/provider.dart';

@RoutePage()
class MfaRecoveryInputScreen extends StatefulWidget implements AutoRouteWrapper {
  const MfaRecoveryInputScreen({super.key});

  @override
  Widget wrappedRoute(BuildContext context) {
    final loginService = context.read<LoginService>();

    return ChangeNotifierProvider(
      create: (ctx) => MfaRecoveryViewModel(loginService: loginService),
      child: this,
    );
  }

  @override
  State<MfaRecoveryInputScreen> createState() => _MfaRecoveryInputScreenState();
}

class _MfaRecoveryInputScreenState extends BaseState<MfaRecoveryInputScreen, MfaRecoveryViewModel> {
  @override
  Widget build(BuildContext context) {
    final isVerifying = context.select<MfaRecoveryViewModel, bool>((value) => value.isVerifying);

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: PopScopeUtil().onWillPop,
      child: GestureDetector(
        onTap: () {
          // Hide soft input keyboard on clicking outside TextField
          FocusManager.instance.primaryFocus?.unfocus();
        },
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          appBar: KcAppBar(
            titleText: context.l10n.enterRecoveryCode,
            height: 55.w,
          ),
          body: SafeArea(
            child: SingleChildScrollView(
              child: AbsorbPointer(
                absorbing: isVerifying,
                child: _buildBody(isVerifying),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBody(bool isVerifying) {
    final hasRecoveryCode = context.select<MfaRecoveryViewModel, bool>((value) => value.hasRecoveryCode);

    final textInputStyle = TextStyle(
      fontSize: 14.w,
      fontWeight: FontWeight.bold,
      color: AppColors.textColor,
      height: 1.29,
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 44.w),
        Text(
          context.l10n.pleaseEnterRecoveryCode,
          textAlign: TextAlign.center,
          style: AppTextTheme.text500Style,
        ),
        SizedBox(height: 32.w),
        Neumorphic(
          margin: EdgeInsets.symmetric(horizontal: 15.w),
          style: neumorphicBaseStyle.copyWith(
            color: const Color.fromRGBO(238, 238, 238, 0.5),
            depth: -2,
            boxShape: NeumorphicBoxShape.roundRect(BorderRadius.circular(15)),
            lightSource: const LightSource(-0.5, -1),
          ),
          child: TextField(
            style: textInputStyle,
            decoration: InputDecoration(
              floatingLabelStyle: const TextStyle(color: AppColors.textColor),
              contentPadding: EdgeInsets.all(15.w),
              border: InputBorder.none,
              isDense: true, // Remove the default content padding.
              hintText: "",
              hintStyle: textInputStyle.copyWith(color: const Color(0xff89898B)),
            ),
            // Hack to open keyboard Half-width alphanumeric character
            keyboardType: Platform.isIOS ? TextInputType.url : TextInputType.visiblePassword,
            minLines: 1,
            maxLines: 1,
            onChanged: (value) => vm.onChangedRecoveryCode(value),
          ),
        ),
        Builder(builder: (BuildContext context) {
          vm.setTooltipContext(context);
          return const SizedBox(height: 0);
        }),
        SizedBox(
          height: 121.w,
          child: Column(
            children: [
              SizedBox(height: 50.w),
              isVerifying ? const DuringVerifyWidget() : const SizedBox.shrink(),
            ],
          ),
        ),
        SizedBox(
          width: 285.w,
          height: 50.w,
          child: ShadowStadiumButton(
            text: context.l10n.send,
            onPressed: !hasRecoveryCode ? null : () => vm.send(),
            bgColor: hasRecoveryCode ? AppColors.primaryColor : AppColors.disableBgColor,
            textStyle: TextStyle(fontSize: 18.w, fontWeight: FontWeight.bold, color: Colors.white, height: 1.25),
          ),
        ),
        SizedBox(height: 30.w),
        SizedBox(
          width: 285.w,
          height: 35.w,
          child: ShadowStadiumButton(
            text: context.l10n.issueRecoveryCode,
            onPressed: () => vm.issueRecoveryCode(),
          ),
        ),
        SizedBox(height: 30.w),
        KcLinkButton(
          text: context.l10n.howToIssueRecoveryCode,
          onTap: () => vm.howToIssueRecoveryCode(),
        ),
        SizedBox(height: 30.w),
        SizedBox(
          width: 285.w,
          height: 35.w,
          child: ShadowStadiumButton(
            text: context.l10n.authAnotherMethod,
            onPressed: () => vm.authAnotherMethod(),
          ),
        ),
        SizedBox(height: 30.w),
      ],
    );
  }
}
