import 'dart:async';
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter_neumorphic/flutter_neumorphic.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:http/http.dart' as http;
import 'package:kc_member_site_native/extensions/build_context_extensions.dart';
import 'package:kc_member_site_native/gen/assets.gen.dart';
import 'package:kc_member_site_native/util/app_dialog.dart';
import 'package:kc_member_site_native/util/device_info_util.dart';
import 'package:kc_member_site_native/widgets/header.dart';
import 'package:path_provider/path_provider.dart';
import 'package:url_launcher/url_launcher.dart';

@RoutePage()
class PdfViewerScreen extends StatefulWidget {
  const PdfViewerScreen(this.pdfUrl, {super.key});
  final String pdfUrl;

  @override
  State<PdfViewerScreen> createState() => _PdfViewerScreenState();
}

class _PdfViewerScreenState extends State<PdfViewerScreen> {
  String _remotePDFpath = "";
  bool _isLoading = false;
  static const pdfFileName = "kcmsr_pdf_in_webview.pdf";
  String _pdfFilePath = "";
  final bool _isPdfUseHybridComposition = DeviceInfoUtil().shouldHybridComposition();

  @override
  void didUpdateWidget(covariant PdfViewerScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.pdfUrl != widget.pdfUrl) {
      _init();
    }
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  void _init() {
    setState(() {
      _isLoading = true;
      _remotePDFpath = "";
    });
    _createFileOfPdfUrl(widget.pdfUrl).then((f) {
      if (!mounted) return;
      setState(() {
        _remotePDFpath = f?.path ?? "";
        if (f == null) {
          _isLoading = false;
        }
      });
    });
  }

  @override
  void dispose() {
    _deletePdfFile();
    super.dispose();
  }

  Future<String> get _getPdfFilePath async {
    if (_pdfFilePath.isEmpty) {
      final dir = await getApplicationDocumentsDirectory();
      _pdfFilePath = "${dir.path}/$pdfFileName";
    }

    debugPrint("_getPdfFilePath $_pdfFilePath");
    return _pdfFilePath;
  }

  Future<FileSystemEntity?> _deletePdfFile() async {
    if (_pdfFilePath.isNotEmpty) {
      try {
        File file = File(_pdfFilePath);
        final result = await file.delete();
        return result;
      } catch (e) {
        debugPrint(e.toString());
      }
    }
    return null;
  }

  Future<File?> _createFileOfPdfUrl(String url) async {
    Completer<File?> completer = Completer();
    debugPrint("Start download Pdf file.");
    try {
      var res = await http.get(Uri.parse(url));
      final bodyString = res.body.trimLeft();
      if (res.statusCode != 200 || bodyString.startsWith("<?xml version=") || bodyString.startsWith("<!DOCTYPE html")) {
        throw Exception('statusCode ${res.statusCode}_${res.reasonPhrase}');
      }
      final filePath = await _getPdfFilePath;
      File file = File(filePath);
      await file.writeAsBytes(res.bodyBytes, flush: true);
      completer.complete(file);
    } catch (e) {
      debugPrint(e.toString());
      completer.complete();
      _handleError();
    }

    return completer.future;
  }

  void _handleError() {
    if (!mounted) return;
    showMessageActionDialog(context, context.l10n.me05, [
      ActionProps(
          onPressed: (context) {
            Navigator.pop(context);
            setState(() {
              _isLoading = false;
            });
          },
          child: Text(context.l10n.close)),
      ActionProps(
          onPressed: (context) {
            Navigator.pop(context);
            _init();
          },
          child: Text(context.l10n.retry)),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: Header(
        leading: [
          NeumorphicButton(
            padding: const EdgeInsets.all(8),
            style: const NeumorphicStyle(
              color: Color(0xfff2f2f2),
              boxShape: NeumorphicBoxShape.circle(),
              intensity: 1,
              shadowDarkColor: Color(0xffd9d9d9),
            ),
            onPressed: () {
              context.router.popForced();
            },
            child: Assets.svgs.close.svg(
              width: 11,
              height: 11,
            ),
          )
        ],
      ),
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        child: Stack(
          children: [
            if (_remotePDFpath.isNotEmpty)
              PDFView(
                useHybridComposition: _isPdfUseHybridComposition,
                pageFling: false,
                pageSnap: false,
                filePath: _remotePDFpath,
                onRender: (_) {
                  setState(() {
                    _isLoading = false;
                  });
                },
                // onError, onPageError (Android only)
                // https://github.com/endigo/flutter_pdfview/blob/780a3d30e27c6a0610f9ad74630d6155fe4ee355/CHANGELOG.md#1005
                onError: (error) {
                  debugPrint("onError ${error.toString()}");
                  _handleError();
                },
                onPageError: (page, error) {
                  debugPrint("onPageError ${error.toString()}");
                  _handleError();
                },
                preventLinkNavigation: true,
                onLinkHandler: (url) {
                  debugPrint("onLinkHandler $url");
                  if (url == null || url.isEmpty) return;
                  final uri = Uri.tryParse(url);
                  if (uri == null) return;
                  launchUrl(uri, mode: LaunchMode.platformDefault);
                },
                onViewCreated: (PDFViewController controller) {
                  if (Platform.isIOS) {
                    // fix: KCMSR-4070 force scroll top
                    Future.delayed(const Duration(milliseconds: 50), () {
                      controller.setPage(0);
                    });
                  }
                },
                autoSpacing: Platform.isIOS,
              ),
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              )
          ],
        ),
      ),
    );
  }
}
