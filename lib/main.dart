import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:chopper/chopper.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_apns/flutter_apns.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:kc_member_site_native/app.dart';
import 'package:kc_member_site_native/constant/base.dart';
import 'package:kc_member_site_native/constant/storage.dart';
import 'package:kc_member_site_native/repository/client/bff_client.dart';
import 'package:kc_member_site_native/repository/client/http_client.dart';
import 'package:kc_member_site_native/service/banhmi_service.dart';
import 'package:kc_member_site_native/service/index.dart';
import 'package:kc_member_site_native/util/app_info.dart';
import 'package:kc_member_site_native/util/app_review.dart';
import 'package:kc_member_site_native/util/device_info_util.dart';
import 'package:kc_member_site_native/util/karte_util.dart';
import 'package:kc_member_site_native/view_model/login_view_model.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'model/timeline_payload_model.dart';
import 'repository/client/kcm_client.dart';
import 'util/make_local_notification.dart';
import 'util/run_javascript.dart';
import 'util/shared_preference.dart';

String apnsToken = '';

String fcmToken = '';

ChopperClient kcmApiClient = KcmApiClientCreator().create();
ChopperClient bffApiClient = BffClientCreator().create();

Future _onLaunch(RemoteMessage remoteMessage, Map<String, dynamic> data) async {
  if (await KarteUtil().isKartePush(remoteMessage)) {
    return;
  }
  debugPrint('_onLaunch');
  debugPrint('apns payload: ${data.toString()}');
  final jsonData = json.encode(data);
  final Map<String, dynamic> remapData = json.decode(jsonData);
  TimelinePayloadModel timelinePayloadModel = TimelinePayloadModel.fromJson(remapData['data']);
  postNotifyData(timelinePayloadModel);
}

Future _onResume(RemoteMessage remoteMessage, Map<String, dynamic> data) async {
  if (await KarteUtil().isKartePush(remoteMessage)) {
    return;
  }
  debugPrint('_onResume');
  debugPrint('apns payload: ${data.toString()}');
  final jsonData = json.encode(data);
  final Map<String, dynamic> remapData = json.decode(jsonData);
  TimelinePayloadModel timelinePayloadModel = TimelinePayloadModel.fromJson(remapData['data']);
  postNotifyData(timelinePayloadModel);
}

@pragma('vm:entry-point')
Future<void> _fcmBackgroundHandler(RemoteMessage remoteMessage) async {
  debugPrint("Handling a background message: ${remoteMessage.messageId}");
  await Firebase.initializeApp();
  if (await KarteUtil().isKartePush(remoteMessage)) {
    makeKarteLocalNotification(remoteMessage.data);
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  if (kReleaseMode) {
    debugPrint = (String? message, {int? wrapWidth}) {
      if (Base.currentEnv == Env.develop) {
        // ignore: avoid_print
        print('kcmsr_log: $message');
      }
    };
  }
  HttpOverrides.global = MyHttpOverrides();

  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);

  sharedPreferences = await SharedPreferences.getInstance();
  final secureStorage = SecureStorageServiceImpl();
  if (sharedPreferences.getBool(StorageKey.firstRun) ?? true) {
    // Ensure no secure storage of old app
    sharedPreferences.setBool(StorageKey.firstRun, false);
    await secureStorage.deleteAll();
  }

  await Future.wait([
    Firebase.initializeApp(),
    AppInfo().init(),
  ]);
  await Future.wait([
    AppReview().init(secureStorage),
    FirebaseAppCheck.instance.activate(
      androidProvider: kDebugMode ? AndroidProvider.debug : AndroidProvider.playIntegrity,
      appleProvider: kDebugMode ? AppleProvider.debug : AppleProvider.deviceCheck,
    ),
    DeviceInfoUtil().init(),
    _initNotification(),
  ]);
  _catchAllError();
  final accountService = AccountServiceImpl();
  final loginService = LoginServiceImpl();

  runApp(
    MultiProvider(
      providers: [
        Provider<AppVersionService>.value(value: AppVersionServiceImpl()),
        Provider<BanhmiService>.value(value: BanhmiServiceImpl()),
        Provider<AccountService>.value(value: accountService),
        Provider<LoginService>.value(value: loginService),
        Provider<SecureStorageService>.value(value: secureStorage),
        Provider<PollingService>.value(value: PollingServiceImpl()),
        ChangeNotifierProvider(
          create: (_) => LoginViewModel(
            accountService: accountService,
            loginService: loginService,
            secureStorage: secureStorage,
          )..init(),
        ),
      ],
      child: const MyApp(),
    ),
  );
}

Future<void> _initNotification() async {
  if (Platform.isAndroid) {
    FirebaseMessaging.onBackgroundMessage(_fcmBackgroundHandler);
    await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );
    await Permission.notification.request();
  } else {
    final apnsConnector = createPushConnector();
    apnsConnector.configure(
      onLaunch: (remoteMessage) => _onLaunch(remoteMessage, remoteMessage.data),
      onResume: (remoteMessage) => _onResume(remoteMessage, remoteMessage.data),
      onMessage: (remoteMessage) async {
        if (await KarteUtil().isKartePush(remoteMessage)) {
          return;
        }
        final jsonData = json.encode(remoteMessage.data);
        final Map<String, dynamic> remapData = json.decode(jsonData);
        await makeLocalNotification(remoteMessage.notification!, remapData['data']);
      },
    );

    apnsConnector.token.addListener(() {
      if (apnsConnector.token.value != null) {
        postDeviceToken(apnsConnector.token.value!);
        apnsToken = apnsConnector.token.value!;
      }
      debugPrint('___apnsToken: $apnsToken');
    });
    apnsConnector.requestNotificationPermissions();
  }

  bool? state = await flnp.initialize(
      const InitializationSettings(
          // [iOS] Breaking change Removed onDidReceiveLocalNotification callback as this was only relevant on iOS versions older than 10
          // iOS: DarwinInitializationSettings(onDidReceiveLocalNotification: onDidReceiveLocation),
          iOS: DarwinInitializationSettings(),
          android: AndroidInitializationSettings('@drawable/ic_notification')),
      onDidReceiveNotificationResponse: onSelectNotification);
  if (state != null) debugPrint(state.toString());

  final notificationAppLaunchDetails = await flnp.getNotificationAppLaunchDetails();
  if (notificationAppLaunchDetails?.didNotificationLaunchApp ?? false) {
    onSelectNotification(notificationAppLaunchDetails!.notificationResponse);
  }
}

void _catchAllError() {
  if (kDebugMode) return;
  FlutterError.onError = (details) {
    if (kReleaseMode) {
      // Pass all uncaught "fatal" errors from the framework to Crashlytics
      FirebaseCrashlytics.instance.recordFlutterFatalError(details);
    }
    FlutterError.presentError(details);
  };
  PlatformDispatcher.instance.onError = (error, stack) {
    if (kReleaseMode) {
      // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    }
    return true;
  };
}
