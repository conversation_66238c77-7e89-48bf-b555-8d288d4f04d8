import 'package:flutter/services.dart';
import 'package:flutter_neumorphic/flutter_neumorphic.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class KCTextField extends StatefulWidget {
  const KCTextField({
    super.key,
    this.placeholder,
    this.onChanged,
    this.keyboardType,
    this.obscureText = false,
    this.textInputAction,
    this.initialValue,
  });
  final String? placeholder;
  final void Function(String)? onChanged;
  final TextInputType? keyboardType;
  final bool obscureText;
  final TextInputAction? textInputAction;
  final String? initialValue;

  @override
  State<KCTextField> createState() => _KCTextFieldState();
}

class _KCTextFieldState extends State<KCTextField> {
  TextEditingController? _controller;
  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
  }

  @override
  void didUpdateWidget(covariant KCTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (_controller != null && widget.initialValue != _controller?.text) {
      _controller!.text = widget.initialValue ?? "";
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Neumorphic(
      style: NeumorphicStyle(
        color: const Color.fromRGBO(238, 238, 238, 0.5),
        depth: -2,
        boxShape: NeumorphicBoxShape.roundRect(BorderRadius.circular(15)),
        shadowLightColorEmboss: const Color.fromRGBO(255, 255, 255, 0.2),
      ),
      child: TextFormField(
        controller: _controller,
        style: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.bold,
          height: 1.5,
          color: const Color.fromRGBO(0x48, 0x48, 0x48, 1),
        ),
        decoration: InputDecoration(
          border: InputBorder.none,
          isDense: true, // Remove the default content padding.
          contentPadding: EdgeInsets.symmetric(horizontal: 27.w, vertical: 13.h),
          hintText: widget.placeholder,
          hintStyle: TextStyle(
            fontSize: 14.sp,
            color: const Color.fromRGBO(0x89, 0x89, 0x8b, 1),
          ),
        ),
        keyboardType: widget.keyboardType,
        onChanged: widget.onChanged,
        obscureText: widget.obscureText,
        obscuringCharacter: "•", // * with vertical align issue
        textAlignVertical: TextAlignVertical.center,
        textInputAction: widget.textInputAction,
        inputFormatters: widget.keyboardType == TextInputType.number
            ? <TextInputFormatter>[FilteringTextInputFormatter.digitsOnly] // Only numbers can be entered
            : null,
      ),
    );
  }
}
