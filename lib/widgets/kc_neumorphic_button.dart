// ignore_for_file: constant_identifier_names, unnecessary_this

import 'package:flutter/services.dart';
import 'package:flutter_neumorphic/flutter_neumorphic.dart';
// import 'package:flutter_neumorphic/src/widget/animation/animated_scale.dart' as animationScale;

/// Rewrite NeumorphicButton to add shadow when button disabled
@immutable
class KcNeumorphicButton extends StatefulWidget {
  static const double PRESSED_SCALE = 0.99;
  static const double UNPRESSED_SCALE = 1.0;

  final Widget? child;
  final NeumorphicStyle? style;
  final double minDistance;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final bool? pressed; //null, true, false
  final Duration duration;
  final Curve curve;
  final NeumorphicButtonClickListener? onPressed;
  final bool drawSurfaceAboveChild;
  final bool provideHapticFeedback;
  final String? tooltip;

  const KcNeumorphicButton({
    Key? key,
    this.padding,
    this.margin = EdgeInsets.zero,
    this.child,
    this.tooltip,
    this.drawSurfaceAboveChild = true,
    this.pressed, //true/false if you want to change the state of the button
    this.duration = Neumorphic.DEFAULT_DURATION,
    this.curve = Neumorphic.DEFAULT_CURVE,
    this.onPressed,
    this.minDistance = 0,
    this.style,
    this.provideHapticFeedback = true,
  }) : super(key: key);

  bool get isEnabled => onPressed != null;

  @override
  State<KcNeumorphicButton> createState() => _NeumorphicButtonState();
}

class _NeumorphicButtonState extends State<KcNeumorphicButton> {
  late NeumorphicStyle initialStyle;

  late double depth;
  bool pressed = false; //overwrite widget.pressed when click for animation

  void updateInitialStyle() {
    final appBarPresent = NeumorphicAppBarTheme.of(context) != null;

    final theme = NeumorphicTheme.currentTheme(context);
    this.initialStyle = widget.style ?? (appBarPresent ? theme.appBarTheme.buttonStyle : (theme.buttonStyle ?? const NeumorphicStyle()));
    depth = widget.style?.depth ?? (appBarPresent ? theme.appBarTheme.buttonStyle.depth : theme.depth) ?? 0.0;

    setState(() {});
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    updateInitialStyle();
  }

  @override
  void didUpdateWidget(KcNeumorphicButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    updateInitialStyle();
  }

  Future<void> _handlePress() async {
    hasFinishedAnimationDown = false;
    setState(() {
      pressed = true;
      depth = widget.minDistance;
    });

    await Future.delayed(widget.duration); //wait until animation finished
    hasFinishedAnimationDown = true;

    //haptic vibration
    if (widget.provideHapticFeedback) {
      HapticFeedback.lightImpact();
    }

    _resetIfTapUp();
  }

  bool hasDisposed = false;

  @override
  void dispose() {
    super.dispose();
    hasDisposed = true;
  }

  //used to stay pressed if no tap up
  void _resetIfTapUp() {
    if (hasFinishedAnimationDown == true && hasTapUp == true && !hasDisposed) {
      setState(() {
        pressed = false;
        depth = initialStyle.depth ?? neumorphicDefaultTheme.depth;

        hasFinishedAnimationDown = false;
        hasTapUp = false;
      });
    }
  }

  bool get clickable {
    return widget.isEnabled && widget.onPressed != null;
  }

  bool hasFinishedAnimationDown = false;
  bool hasTapUp = false;

  @override
  Widget build(BuildContext context) {
    final result = _build(context);
    if (widget.tooltip != null) {
      return Tooltip(
        message: widget.tooltip!,
        child: result,
      );
    } else {
      return result;
    }
  }

  Widget _build(BuildContext context) {
    final appBarPresent = NeumorphicAppBarTheme.of(context) != null;
    final appBarTheme = NeumorphicTheme.currentTheme(context).appBarTheme;

    return GestureDetector(
      onTapDown: (detail) {
        hasTapUp = false;
        if (clickable && !pressed) {
          _handlePress();
        }
      },
      onTapUp: (details) {
        if (clickable) {
          widget.onPressed!();
        }
        hasTapUp = true;
        _resetIfTapUp();
      },
      onTapCancel: () {
        hasTapUp = true;
        _resetIfTapUp();
      },
      child: AnimatedScale(
        duration: widget.duration,
        scale: _getScale(),
        child: Neumorphic(
          margin: widget.margin ?? const EdgeInsets.all(0),
          drawSurfaceAboveChild: widget.drawSurfaceAboveChild,
          duration: widget.duration,
          curve: widget.curve,
          padding: widget.padding ??
              (appBarPresent ? appBarTheme.buttonPadding : null) ??
              const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
          style: initialStyle.copyWith(
            depth: _getDepth(),
          ),
          child: widget.child,
        ),
      ),
    );
  }

  double _getDepth() {
    return this.depth;
  }

  double _getScale() {
    if (widget.pressed != null) {
      //defined by the widget that use it
      return widget.pressed! ? KcNeumorphicButton.PRESSED_SCALE : KcNeumorphicButton.UNPRESSED_SCALE;
    } else {
      return this.pressed ? KcNeumorphicButton.PRESSED_SCALE : KcNeumorphicButton.UNPRESSED_SCALE;
    }
  }
}
