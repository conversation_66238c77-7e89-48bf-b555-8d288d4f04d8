import 'package:flutter_neumorphic/flutter_neumorphic.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/extensions/color_extensions.dart';
import 'package:kc_member_site_native/gen/assets.gen.dart';
import 'package:kc_member_site_native/themes/app_colors.dart';
import 'package:kc_member_site_native/themes/app_text_theme.dart';
import 'package:kc_member_site_native/widgets/kc_neumorphic_button.dart';

class KcCheckbox extends StatelessWidget {
  final bool value;
  final NeumorphicCheckboxListener onChanged;
  final EdgeInsets margin;
  final String? title;

  const KcCheckbox({
    super.key,
    required this.value,
    required this.onChanged,
    this.margin = const EdgeInsets.all(0),
    this.title,
  });

  bool get isSelected => value;

  void _onClick() {
    onChanged(!value);
  }

  @override
  Widget build(BuildContext context) {
    Color iconColor = isSelected ? AppColors.primaryColor : AppColors.gray100Color;

    return GestureDetector(
      onTap: _onClick,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          KcNeumorphicButton(
            pressed: false, // Disable scale animation
            padding: EdgeInsets.all(6.w),
            margin: margin,
            onPressed: _onClick,
            style: NeumorphicStyle(
              boxShape: const NeumorphicBoxShape.circle(),
              color: AppColors.mainBgColor,
              shadowDarkColor: AppColors.dropShadowColor,
              shadowDarkColorEmboss: AppColors.dropShadowColor,
              shadowLightColor: Colors.white,
              shadowLightColorEmboss: Colors.white,
              depth: isSelected ? -1.4 : 4,
              intensity: 1,
            ),
            minDistance: -1.4, // Press state
            child: Assets.svgs.check.svg(colorFilter: iconColor.toColorFilter, width: 13.w),
          ),
          title != null ? Text(title!, style: AppTextTheme.text500Style.copyWith(fontSize: 12.w)) : const SizedBox.shrink(),
        ],
      ),
    );
  }
}
