import 'package:flutter_neumorphic/flutter_neumorphic.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class KCSwitch extends StatelessWidget {
  const KCSwitch({
    super.key,
    required this.value,
    required this.onChanged,
    this.title,
    this.isDisable = false,
  });
  final bool value;
  final void Function(bool) onChanged;
  final String? title;
  final bool isDisable;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (title != null) ...[
          Text(title!,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: const Color.fromRGBO(0x48, 0x48, 0x48, 1),
              )),
          Padding(padding: EdgeInsets.only(right: 15.w)),
        ],
        Neumorphic(
          style: const NeumorphicStyle(boxShape: NeumorphicBoxShape.stadium()),
          child: <PERSON><PERSON><PERSON><PERSON>(
            width: 45.h,
            height: 25.h,
            child: NeumorphicSwitch(
              style: NeumorphicSwitchStyle(
                  disableDepth: true,
                  activeTrackColor: Theme.of(context).primaryColor.withOpacity(isDisable ? 0.5 : 1),
                  inactiveTrackColor: const Color.fromRGBO(0x89, 0x89, 0x8b, 1),
                  activeThumbColor: const Color.fromRGBO(0xf4, 0xf4, 0xf4, 1),
                  inactiveThumbColor: const Color.fromRGBO(0xf4, 0xf4, 0xf4, 1)),
              value: value,
              onChanged: isDisable ? null : onChanged,
            ),
          ),
        ),
      ],
    );
  }
}
