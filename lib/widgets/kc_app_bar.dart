import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/themes/app_colors.dart';

class KcAppBar extends AppBar {
  KcAppBar({
    Key? key,
    Widget? title,
    Widget? leading,
    this.height,
    this.titleText,
    List<Widget>? actions,
    bool? centerTitle,
  }) : super(
          key: key,
          title: titleText != null
              ? Text(
                  titleText,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14.w,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textColor,
                  ),
                )
              : title,
          leading: leading,
          centerTitle: centerTitle ?? true,
          actions: actions == null ? actions : [...actions, SizedBox(width: 20.w)],
          automaticallyImplyLeading: false,
          elevation: 0,
          backgroundColor: Colors.transparent,
        );

  final double? height;
  final String? titleText;

  @override
  Size get preferredSize => Size.fromHeight(height ?? 40.w);
}
