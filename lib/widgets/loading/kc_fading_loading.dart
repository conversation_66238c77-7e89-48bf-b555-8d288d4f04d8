import 'dart:math' as math;

import 'package:flutter/cupertino.dart';

/// ref: https://api.flutter.dev/flutter/cupertino/CupertinoActivityIndicator-class.html
class KcFadingLoading extends StatefulWidget {
  const KcFadingLoading({
    super.key,
    required this.color,
    this.animating = true,
    this.radius = 10.0,
    this.lineWidth = 10.0,
    this.rx = 3.0,
  })  : assert(radius > 0.0),
        progress = 1.0;

  const KcFadingLoading.partiallyRevealed({
    super.key,
    required this.color,
    this.radius = 10.0,
    this.progress = 1.0,
    this.lineWidth = 10.0,
    this.rx = 3.0,
  })  : assert(radius > 0.0),
        assert(progress >= 0.0),
        assert(progress <= 1.0),
        animating = false;

  final double lineWidth;
  final Color color;
  final bool animating;
  final double radius;
  final double progress;
  final double rx;

  @override
  State<KcFadingLoading> createState() => _KcFadingLoadingState();
}

class _KcFadingLoadingState extends State<KcFadingLoading> with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );

    if (widget.animating) {
      _controller.repeat();
    }
  }

  @override
  void didUpdateWidget(KcFadingLoading oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.animating != oldWidget.animating) {
      if (widget.animating) {
        _controller.repeat();
      } else {
        _controller.stop();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.radius * 2,
      width: widget.radius * 2,
      child: CustomPaint(
        painter: _KcFadingLoadingPainter(
          rx: widget.rx,
          position: _controller,
          activeColor: widget.color,
          radius: widget.radius,
          progress: widget.progress,
          lineWidth: widget.lineWidth,
        ),
      ),
    );
  }
}

const double _kTwoPI = math.pi * 2.0;

const List<int> _kAlphaValues = <int>[
  51, // Opacity 0.2
  51,
  51,
  51,
  102, // Opacity 0.4
  153, // Opacity 0.6
  204, // Opacity 0.8
  255, // Opacity 1
];

const int _partiallyRevealedAlpha = 255;

class _KcFadingLoadingPainter extends CustomPainter {
  _KcFadingLoadingPainter({
    required this.position,
    required this.activeColor,
    required this.radius,
    required this.progress,
    required this.lineWidth,
    required this.rx,
  })  : tickFundamentalRRect = RRect.fromLTRBXY(
          -radius / lineWidth,
          -radius / rx,
          radius / lineWidth,
          -radius,
          radius / lineWidth,
          radius / lineWidth,
        ),
        super(repaint: position);

  final Animation<double> position;
  final Color activeColor;
  final double radius;
  final double progress;
  final double lineWidth;
  final double rx;

  final RRect tickFundamentalRRect;

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint();
    final int tickCount = _kAlphaValues.length;

    canvas.save();
    canvas.translate(size.width / 2.0, size.height / 2.0);

    final int activeTick = (tickCount * position.value).floor();

    for (int i = 0; i < tickCount * progress; ++i) {
      final int t = (i - activeTick) % tickCount;
      paint.color = activeColor.withAlpha(progress < 1 ? _partiallyRevealedAlpha : _kAlphaValues[t]);
      canvas.drawRRect(tickFundamentalRRect, paint);
      canvas.rotate(_kTwoPI / tickCount);
    }

    canvas.restore();
  }

  @override
  bool shouldRepaint(_KcFadingLoadingPainter oldPainter) {
    return oldPainter.position != position || oldPainter.activeColor != activeColor || oldPainter.progress != progress;
  }
}
