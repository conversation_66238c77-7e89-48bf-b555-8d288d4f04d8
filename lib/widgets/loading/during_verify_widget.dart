import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/extensions/build_context_extensions.dart';
import 'package:kc_member_site_native/themes/app_colors.dart';
import 'package:kc_member_site_native/themes/app_text_theme.dart';
import 'package:kc_member_site_native/widgets/loading/kc_fading_loading.dart';

class DuringVerifyWidget extends StatelessWidget {
  const DuringVerifyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        KcFadingLoading(color: AppColors.gray200Color, radius: 11.w),
        SizedBox(width: 10.w),
        Text(context.l10n.duringVerify, style: AppTextTheme.text500Style),
      ],
    );
  }
}
