import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/extensions/string_extensions.dart';
import 'package:kc_member_site_native/gen/assets.gen.dart';
import 'package:kc_member_site_native/themes/app_colors.dart';
import 'package:kc_member_site_native/themes/app_text_theme.dart';
import 'package:kc_member_site_native/util/show_menu.dart';

class ItemOption<T> {
  const ItemOption({required this.name, required this.value});
  final String name;
  final T value;
}

class KcDropdown<T> extends StatefulWidget {
  const KcDropdown({
    super.key,
    required this.options,
    this.onChange,
    this.initialValue,
    this.isEnable = true,
  });
  final List<ItemOption> options;
  final void Function(T)? onChange;
  final T? initialValue;
  final bool isEnable;

  @override
  State<KcDropdown<T>> createState() => _KcDropdownState<T>();
}

const border = OutlineInputBorder(
  borderSide: BorderSide(color: AppColors.gray200Color, width: 1),
  borderRadius: BorderRadius.all(Radius.circular(3)),
);

class _KcDropdownState<T> extends State<KcDropdown<T>> with SingleTickerProviderStateMixin {
  late T? _itemSelected = widget.initialValue;
  late AnimationController _rotationController;

  @override
  void initState() {
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
      upperBound: 0.5,
    );
    super.initState();
  }

  @override
  void dispose() {
    _rotationController.dispose();
    super.dispose();
  }

  void showButtonMenu() {
    final PopupMenuThemeData popupMenuTheme = PopupMenuTheme.of(context);
    final RenderBox button = context.findRenderObject()! as RenderBox;
    final RenderBox overlay = Navigator.of(context).overlay!.context.findRenderObject()! as RenderBox;
    final Offset offset = Offset(0.0, button.size.height);
    final RelativeRect position = RelativeRect.fromRect(
      Rect.fromPoints(
        button.localToGlobal(offset, ancestor: overlay),
        button.localToGlobal(button.size.bottomRight(Offset.zero) + offset, ancestor: overlay),
      ),
      Offset.zero & overlay.size,
    );
    final List<PopupMenuEntry<T>> items = widget.options
        .map(
          (e) => PopupMenuItem<T>(
            value: e.value,
            padding: EdgeInsets.symmetric(horizontal: 15.w),
            height: 30.w,
            child: SizedBox(
              height: 30.w,
              child: Text(
                e.name.useCorrectEllipsis(),
                style: AppTextTheme.text500Style.copyWith(height: 1.7),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        )
        .toList();
    // Only show the menu if there is something to show
    if (items.isNotEmpty) {
      setState(() {
        _rotationController.forward(from: 0);
      });
      kcShowMenu<T?>(
        context: context,
        elevation: 4,
        items: items,
        initialValue: widget.initialValue,
        position: position,
        shape: popupMenuTheme.shape,
        color: popupMenuTheme.color,
        constraints: BoxConstraints(minWidth: 285.w, maxWidth: 285.w),
      ).then<void>((T? newValue) {
        if (!mounted) {
          return null;
        }
        _rotationController.reverse(from: 1);
        if (newValue == null) {
          setState(() {});
          return null;
        }
        if (newValue == _itemSelected) return;
        _itemSelected = newValue;
        setState(() {});
        widget.onChange?.call(newValue);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final canSelect = widget.options.length >= 2;
    return Directionality(
      textDirection: TextDirection.rtl,
      child: OutlinedButton.icon(
        onPressed: (widget.isEnable && canSelect) ? showButtonMenu : null,
        style: OutlinedButton.styleFrom(
          side: const BorderSide(width: 1.0, color: AppColors.gray200Color),
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(3)),
          ),
          padding: EdgeInsets.only(left: 15.w, right: 11.w),
          minimumSize: Size(double.infinity, 30.w),
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
        label: Directionality(
          textDirection: TextDirection.ltr,
          child: Row(
            children: [
              Flexible(
                child: Text(
                  widget.options.firstWhereOrNull((e) => e.value == _itemSelected)?.name.useCorrectEllipsis() ?? "",
                  style: AppTextTheme.text500Style.copyWith(height: 1.2),
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.left,
                ),
              ),
            ],
          ),
        ),
        icon: canSelect
            ? RotationTransition(
                turns: Tween(begin: 0.0, end: 1.0).animate(_rotationController),
                child: Assets.svgs.arrowDown.svg(width: 9.w),
              )
            : const SizedBox.shrink(),
      ),
    );
  }
}
