import 'package:flutter/material.dart';

class Header extends StatelessWidget implements PreferredSizeWidget {
  const Header({
    Key? key,
    this.leading,
    this.height,
    this.leadingPadding,
    this.backgroundColor,
  }) : super(key: key);

  final List<Widget>? leading;
  final double? height;
  final double? leadingPadding;
  final Color? backgroundColor;

  @override
  Size get preferredSize => Size.fromHeight(height ?? 44);

  @override
  Widget build(BuildContext context) {
    final color = Theme.of(context).appBarTheme;

    return Container(
      color: backgroundColor ?? color.backgroundColor,
      child: SafeArea(
        child: SizedBox(
          height: preferredSize.height,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: [..._buildLeading(), const Spacer()],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildLeading() {
    return [SizedBox(width: leadingPadding ?? 15), ...(leading ?? [])];
  }
}
