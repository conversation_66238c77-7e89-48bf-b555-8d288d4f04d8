import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/themes/app_text_theme.dart';

class KcLinkButton extends StatelessWidget {
  const KcLinkButton({
    super.key,
    required this.text,
    this.onTap,
  });
  final void Function()? onTap;
  final String text;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(3),
      onTap: onTap,
      child: Text(
        text,
        style: AppTextTheme.textLinkStyle.copyWith(
          height: AppTextTheme.height18_14,
          fontSize: 14.w,
        ),
      ),
    );
  }
}
