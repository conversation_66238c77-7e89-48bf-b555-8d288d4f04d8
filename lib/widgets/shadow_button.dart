import 'package:flutter_neumorphic/flutter_neumorphic.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/themes/app_colors.dart';
import 'package:kc_member_site_native/widgets/kc_neumorphic_button.dart';

const neumorphicBaseStyle = NeumorphicStyle(
  intensity: 1,
  color: Colors.transparent,
  shadowDarkColor: AppColors.dropShadowColor,
  shadowDarkColorEmboss: AppColors.dropShadowColor,
  shadowLightColor: Colors.white,
  shadowLightColorEmboss: Color.fromRGBO(255, 255, 255, 0.2),
);

class ShadowCircleButton extends KcNeumorphicButton {
  ShadowCircleButton({
    Key? key,
    Widget? child,
    EdgeInsets? padding,
    void Function()? onPressed,
  }) : super(
          key: key,
          child: child,
          padding: padding,
          onPressed: onPressed,
          style: neumorphicBaseStyle.copyWith(
            boxShape: const NeumorphicBoxShape.circle(),
            depth: 3,
            lightSource: const LightSource(-1, -0.4),
          ),
        );
}

class ShadowStadiumButton extends KcNeumorphicButton {
  final Color? bgColor;
  final String? text;
  final TextStyle? textStyle;
  ShadowStadiumButton({
    Key? key,
    Widget? child,
    EdgeInsets? padding,
    EdgeInsets? margin,
    void Function()? onPressed,
    this.bgColor,
    this.text,
    this.textStyle,
    NeumorphicStyle? style,
  }) : super(
          key: key,
          child: text != null
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      text,
                      style: textStyle ??
                          TextStyle(
                            fontSize: 12.w,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textColor,
                            height: 1.25,
                          ),
                    ),
                  ],
                )
              : child,
          padding: padding ?? EdgeInsets.zero,
          margin: margin,
          onPressed: onPressed,
          minDistance: 1,
          style: style ??
              neumorphicBaseStyle.copyWith(
                color: bgColor,
                boxShape: const NeumorphicBoxShape.stadium(),
              ),
        );
}
