import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kc_member_site_native/themes/app_colors.dart';
import 'package:kc_member_site_native/themes/app_text_theme.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

class KcPinCodeWidget extends StatefulWidget {
  const KcPinCodeWidget({
    super.key,
    required this.textEditingController,
    this.onCompleted,
    this.onChanged,
    this.focusNode,
  });

  final TextEditingController textEditingController;
  final ValueChanged<String>? onCompleted;
  final ValueChanged<String>? onChanged;
  final FocusNode? focusNode;

  @override
  State<StatefulWidget> createState() => _KcPinCodeState();
}

class _KcPinCodeState extends State<KcPinCodeWidget> {
  static const boxShadow = [
    BoxShadow(
      color: AppColors.dropShadowColor,
    ),
    BoxShadow(
      offset: Offset(2, 2),
      color: AppColors.mainBgColor,
      spreadRadius: 0,
      blurRadius: 4,
    ),
  ];

  final _numericRe = RegExp('[0-9]');
  static const _textLenght = 6;

  @override
  Widget build(BuildContext context) {
    return PinCodeTextField(
      focusNode: widget.focusNode,
      appContext: context,
      autoFocus: true,
      length: _textLenght,
      animationDuration: const Duration(milliseconds: 100),
      animationType: AnimationType.fade,
      pinTheme: PinTheme(
        shape: PinCodeFieldShape.box,
        borderRadius: BorderRadius.circular(15.w),
        fieldHeight: 70.w,
        fieldWidth: 49.w,
        inactiveFillColor: AppColors.pinCodeBgColor,
        selectedFillColor: AppColors.pinCodeBgColor,
        activeFillColor: AppColors.pinCodeBgColor,
        selectedColor: AppColors.primaryColor,
        activeColor: AppColors.mainBgColor,
        inactiveColor: AppColors.mainBgColor,
        borderWidth: 1,
        activeBoxShadow: boxShadow,
        inActiveBoxShadow: boxShadow,
      ),
      boxShadows: boxShadow,
      cursorColor: AppColors.primaryColor,
      enableActiveFill: true,
      controller: widget.textEditingController,
      keyboardType: TextInputType.number,
      textStyle: AppTextTheme.textNumberStyle.copyWith(fontSize: 36.w),
      cursorHeight: 36.w,
      onCompleted: widget.onCompleted,
      onChanged: widget.onChanged,
      separatorBuilder: (context, index) {
        if (index == 2) {
          return SizedBox(width: 15.w);
        }
        return SizedBox(width: 8.w);
      },
      beforeTextPaste: (pastedText) {
        return false; // Prevent show default dialog of lib
      },
      errorTextSpace: 0,
      inputFormatters: [
        FilteringTextInputFormatter.allow(_numericRe),
      ],
    );
  }
}
