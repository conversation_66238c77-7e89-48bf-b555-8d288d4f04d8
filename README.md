# kc_member_site_native

三菱UFJ eスマート証券のスマートフォンサイトをリニューアルする。

## Prerequisites Installation

- Dart >=3.5.0
- Flutter 3.24.5
- Xcode 16.2
- JDK version 17
- IDE: VSCode
- fvm: https://fvm.app/documentation/getting-started/installation
- make: https://formulae.brew.sh/formula

## CLI start debug in VSCode (only for first clone project)

Run the following commands

```bash
export JAVA_HOME=$(/usr/libexec/java_home -v17)

flutter config --jdk-dir <path-to-java-sdk-17-home-directory>
```

```bash
fvm use 3.24.5
```

```bash
make clean
```

Then close VSCode & start again to apply fvm flutter version

See env config run & debug in ".vscode/launch.json" file to start Run Debug

## How to pass check geo

Connect Japan VPN or comment out this code

```dart
final geo = await _bmService.getGeoLocation()
```

## How to pass App Check Token in iOS simulator

Run with stgA env

## How to use localhost to inspect in browser

Find this code

```dart
context.router.replace(WebViewRoute(initialUrl: result.targetUrl!))
```

and replace by your localhost
Ex:

```dart
context.router.replace(WebViewRoute(initialUrl: "http://************:8081"))
```

> with iOS

- Un comment out this code:
  // (controller.platform as WebKitWebViewController).setInspectable(Base.currentEnv != Env.production);
- iOS version < 16

## Wiki

- https://guide.backlog.com/wiki/KCMSR/Home

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://flutter.dev/docs/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://flutter.dev/docs/cookbook)

For help getting started with Flutter, view our
[online documentation](https://flutter.dev/docs), which offers tutorials,
samples, guidance on mobile development, and a full API reference.

### Build CLI

See Makefile (in root project)

### Check logs

```bash
flutter logs
```

### Notification Icons size

```code
MDPI(Baseline): 24px (22px inset)
HDPI          : 36px
XHDPI         : 48px
XXHDPI        : 72px
XXXHDPI       : 96px
```

### Enable Analytics debug mode on an Android device

- https://firebase.google.com/docs/analytics/debugview#android
