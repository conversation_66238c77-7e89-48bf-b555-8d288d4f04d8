import 'package:flutter_test/flutter_test.dart';
import 'package:kc_member_site_native/model/mfa_model.dart';
import 'package:kc_member_site_native/service/index.dart';

void main() {
  test('checkPreferredMfaType', () {
    final loginService = LoginServiceImpl();

    final res0 = loginService.checkPreferredMfaType([]);
    expect(res0.isMfaPush, false);
    expect(res0.isMfaEmail, false);
    expect(res0.isMfaRecovery, false);

    final res00 = loginService.checkPreferredMfaType([
      const MfaAuthenticatorsModel(active: false, oob_channel: "auth0", authenticator_type: "oob"), // push
      const MfaAuthenticatorsModel(active: true, oob_channel: "email", authenticator_type: "oob"), // email
      const MfaAuthenticatorsModel(active: false, authenticator_type: "recovery-code"), // recovery
    ]);
    expect(res00.isMfaPush, false);
    expect(res00.isMfaEmail, true);
    expect(res00.isMfaRecovery, false);

    final res1 = loginService.checkPreferredMfaType([
      const MfaAuthenticatorsModel(active: true, oob_channel: "auth0", authenticator_type: "oob"), // push
      const MfaAuthenticatorsModel(active: true, oob_channel: "email", authenticator_type: "oob"), // email
      const MfaAuthenticatorsModel(active: true, authenticator_type: "recovery-code"), // recovery
    ]);
    expect(res1.isMfaPush, true);
    expect(res1.isMfaEmail, true);
    expect(res1.isMfaRecovery, true);

    final res2 = loginService.checkPreferredMfaType([
      const MfaAuthenticatorsModel(active: true, authenticator_type: "recovery-code"), // recovery
      const MfaAuthenticatorsModel(active: true, oob_channel: "auth0", authenticator_type: "oob"), // push
      const MfaAuthenticatorsModel(active: true, oob_channel: "email", authenticator_type: "oob"), // email
    ]);
    expect(res2.isMfaPush, true);
    expect(res2.isMfaEmail, true);
    expect(res2.isMfaRecovery, true);

    final res3 = loginService.checkPreferredMfaType([
      const MfaAuthenticatorsModel(active: true, oob_channel: "auth0", authenticator_type: "oob"), // push
      const MfaAuthenticatorsModel(active: true, authenticator_type: "recovery-code"), // recovery
      const MfaAuthenticatorsModel(active: true, oob_channel: "auth0", authenticator_type: "oob"), // push
      const MfaAuthenticatorsModel(active: true, oob_channel: "email", authenticator_type: "oob"), // email
      const MfaAuthenticatorsModel(active: true, oob_channel: "auth0", authenticator_type: "oob"), // push
      const MfaAuthenticatorsModel(active: true, oob_channel: "email", authenticator_type: "oob"), // email
      const MfaAuthenticatorsModel(active: true, oob_channel: "auth0", authenticator_type: "oob"), // push
    ]);
    expect(res3.isMfaPush, true);
    expect(res3.isMfaEmail, true);
    expect(res3.isMfaRecovery, true);
  });
}
