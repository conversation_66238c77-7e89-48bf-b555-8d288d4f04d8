import 'package:flutter_test/flutter_test.dart';
import 'package:kc_member_site_native/extensions/string_extensions.dart';

void main() {
  test('is not Pdf Url', () {
    expect("https://guide.inc/document?file=dd.pdf".isPdfUrl(), false);
    expect("https://guide.inc/document".isPdfUrl(), false);
    expect("https://guide.inc/documentpdf".isPdfUrl(), false);
    expect("https://guide.inc/document/pdf".isPdfUrl(), false);
  });

  test('is Pdf Url', () {
    expect("https://guide.inc/document/.pdf?sss=aapdf".isPdfUrl(), true);
    expect("https://guide.inc/document/doc.pdf?ddd=2".isPdfUrl(), true);
    expect("https://guide.inc/document/.pdf".isPdfUrl(), true);
    expect("https://guide.inc/document.pdf".isPdfUrl(), true);
    expect("https://guide.inc/document.Pdf".isPdfUrl(), true);
  });
}
