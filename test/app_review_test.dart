import 'package:flutter_test/flutter_test.dart';
import 'package:kc_member_site_native/model/app_config_model.dart';
import 'package:kc_member_site_native/util/app_review.dart';

void main() {
  test('matchUrl', () {
    expect(AppReview().matchUrl("/mobile/mypage/performance"), "/mobile/mypage/performance");
    expect(AppReview().matchUrl("/mobile/mypage/performance?a=2"), "/mobile/mypage/performance");
    expect(AppReview().matchUrl("/iPhone/Trade/ToushinBuy/IB01103.asp"), "/iPhone/Trade/ToushinBuy/IB01103.asp");
    expect(AppReview().matchUrl("https://kabu.com/iPhone/Trade/ToushinBuy/IB01103.asp"), "/iPhone/Trade/ToushinBuy/IB01103.asp");
    expect(AppReview().matchUrl(""), null);
    expect(AppReview().matchUrl("aaa"), null);
  });

  test('conditionsShowReview', () {
    final config = AppConfigModel.fromJson({
      "appReview": {"startDate": "2024-04-02 00:00:00", "endDate": "2024-04-09 23:59:59"},
      "app_config": {
        "wl": {
          "domain": [
            "s10-a.dev2.devcabu.jp",
          ]
        },
        "openwithbrowser": {
          "urlincludes": [
            "/iphone/appmenu/MenuCushion4TfxSso_",
          ]
        }
      }
    });
    final AppReviewModel(:startDate, :endDate) = config.appReview!;

    expect(AppReview().matchDate(DateTime(2024, 03, 21), startDate: startDate, endDate: endDate), false);
    expect(AppReview().matchDate(DateTime(2024, 4, 2), startDate: startDate, endDate: endDate), false);
    expect(AppReview().matchDate(DateTime(2024, 4, 2, 1), startDate: startDate, endDate: endDate), true);
    expect(AppReview().matchDate(DateTime(2024, 4, 9), startDate: startDate, endDate: endDate), true);
    expect(AppReview().matchDate(DateTime(2024, 4, 9, 23, 59, 58), startDate: startDate, endDate: endDate), true);
    expect(AppReview().matchDate(DateTime(2024, 4, 9, 23, 59, 59), startDate: startDate, endDate: endDate), false);
    expect(AppReview().matchDate(DateTime(2024, 4, 10), startDate: startDate, endDate: endDate), false);

    final config2 = AppConfigModel.fromJson({
      "app_config": {
        "wl": {
          "domain": [
            "s10-a.dev2.devcabu.jp",
          ]
        },
        "openwithbrowser": {
          "urlincludes": [
            "/iphone/appmenu/MenuCushion4TfxSso_",
          ]
        }
      }
    });
    final appReview = config2.appReview;
    expect(AppReview().matchDate(DateTime(2024, 4, 10), startDate: appReview?.startDate, endDate: appReview?.endDate), false);
  });

  test('checkInstallDate', () async {
    expect(AppReview().checkInstallDate(currentDate: DateTime(2024, 03, 28), installDateString: "2024-03-22T20:18:04.000Z"), true);
    expect(AppReview().checkInstallDate(currentDate: DateTime(2024, 03, 29), installDateString: "2024-03-22T20:18:04.000Z"), true);
    expect(AppReview().checkInstallDate(currentDate: DateTime(2024, 03, 27), installDateString: "2024-03-22T20:18:04.000Z"), false);
    expect(AppReview().checkInstallDate(currentDate: DateTime(2025, 04, 10), installDateString: "2024-03-22T20:18:04.000Z"), true);
  });
}
