name: kc_member_site_native
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0+100

environment:
  sdk: ">=3.5.0"
  flutter: "3.24.5"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # https://github.com/flutter/flutter/issues/94591
  webview_flutter: ^4.10.0
  firebase_core: ^3.7.0
  firebase_messaging: ^15.1.4
  device_info_plus: ^11.1.1
  flutter_apns:
    git:
      url: https://github.com/guide-inc-org/guide-flutter-apns.git
      ref: kc_guide/flutter_latest
      path: flutter_apns
  flutter_local_notifications: ^18.0.1
  http: ^1.2.2
  flutter_secure_storage: ^9.2.2
  url_launcher: ^6.3.0
  provider: ^6.1.2
  chopper: ^8.0.3
  json_annotation: ^4.9.0
  package_info_plus: ^8.1.1
  flutter_neumorphic:
    git:
      url: https://github.com/guide-inc-org/guide-flutter_Flutter-Neumorphic.git
      ref: kc_guide/flutter_latest
  permission_handler: ^11.3.1
  native_flutter_proxy:
    git:
      url: https://github.com/guide-inc-org/guide-native_flutter_proxy.git
      ref: kc_guide/agp_8
  flutter_screenutil: ^5.9.3
  webview_cookie_manager:
    git:
      url: https://github.com/guide-inc-org/guide-flutter_webview_cookie_manager.git
      ref: kc_guide/agp_8
  shared_preferences: ^2.5.3
  move_to_background:
    git:
      url: https://github.com/guide-inc-org/guide-flutter_move_to_background.git
      ref: kc_guide/agp_8
  flutter_inappwebview: ^6.1.5
  auto_route: ^9.2.2
  flutter_pdfview:
    git:
      url: https://github.com/guide-inc-org/guide-flutter_pdfview.git
      ref: kc_guide/flutter_latest
  path_provider: ^2.1.5
  firebase_crashlytics: ^4.1.4
  flutter_jailbreak_detection:
    git:
      url: https://github.com/guide-inc-org/guide-flutter_jailbreak_detection.git
      ref: kc_guide
  flutter_svg: ^2.0.14
  firebase_app_check: ^0.3.1+5
  collection: ^1.18.0
  easy_debounce: ^2.0.3
  firebase_analytics: ^11.3.4
  pin_code_fields:
    git:
      url: https://github.com/guide-inc-org/guide-flutter_pin_code_fields.git
      ref: kc_guide/flutter_latest
  super_tooltip:
    git:
      url: https://github.com/guide-inc-org/guide-flutter_super_tooltip.git
      ref: v1.0.2
  async: ^2.11.0
  uni_links:
    git:
      url: https://github.com/guide-inc-org/guide-flutter_uni_links.git
      path: "uni_links"
      ref: kc_guide/agp_8
  flutter_web_browser:
    git:
      url: https://github.com/guide-inc-org/guide-flutter_web_browser.git
      ref: kc_guide/agp_8
  karte_core:
    git:
      url: https://github.com/guide-inc-org/guide-flutter_karte-flutter.git
      path: karte_core
      ref: kc_guide/agp_8
  karte_notification:
    git:
      url: https://github.com/guide-inc-org/guide-flutter_karte-flutter.git
      path: karte_notification
      ref: kc_guide/agp_8
  in_app_review: ^2.0.9
  intl: ^0.19.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.13
  chopper_generator: ^8.0.3
  json_serializable: ^6.2.0

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.1
  auto_route_generator: ^9.0.0
  flutter_launcher_icons: ^0.14.1
  flutter_gen_runner: ^5.8.0

flutter_gen:
  output: lib/gen/
  integrations:
    flutter_svg: true

dependency_overrides:
  webview_flutter_android:
    git:
      url: https://github.com/guide-inc-org/guide-flutter_packages.git
      path: "packages/webview_flutter/webview_flutter_android"
      ref: kcmsr/webview_flutter_android
  webview_flutter_wkwebview:
    git:
      url: https://github.com/guide-inc-org/guide-flutter_packages.git
      path: "packages/webview_flutter/webview_flutter_wkwebview"
      ref: kcmsr/webview_flutter_wkwebview

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  generate: true
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/svgs/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  fonts:
    - family: NotoSansJP
      fonts:
        - asset: assets/fonts/NotoSansJP-Regular.otf
          weight: 400
        - asset: assets/fonts/NotoSansJP-Medium.otf
          weight: 500
        - asset: assets/fonts/NotoSansJP-Bold.otf
          weight: 700
    - family: OpenSans
      fonts:
        - asset: assets/fonts/OpenSans-Regular.ttf
          weight: 400
        - asset: assets/fonts/OpenSans-SemiBold.ttf
          weight: 600
