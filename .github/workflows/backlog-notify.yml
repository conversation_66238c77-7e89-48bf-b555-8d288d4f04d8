on:
  push:
    branches:
      - develop
      - main
  pull_request:
    types:
      - opened
      - reopened
      - ready_for_review
      - closed
    branches:
      - feature/**
      - fix/**
      - refactor/**
      - develop
      - release/**
      - main

permissions:
  contents: read
  pull-requests: write

jobs:
  notify:
    runs-on: ubuntu-latest
    steps:
      - name: Backlog Notify
        uses: bicstone/backlog-notify@v4
        with:
          # 必須設定 (The following are required settings)
          project_key: COINCHECK
          api_host: guide.backlog.com
          api_key: ${{ secrets.BACKLOG_API_KEY }}

          push_comment_template: |-
            <%= commits[0].author.name %> pushed to [[<%= ref.name %>:<%= ref.url %>]]
            <% commits.forEach(commit=>{ %>
            + [[<%= commit.comment %> (<% print(commit.id.slice(0, 7)) %>):<%= commit.url %>]]<% }); %>
          pr_opened_comment_template: |-
            <%= sender.login %> created pull request
            
            + [<%= title %>] <%= pr.html_url %>  (#<%= pr.number %>)
          pr_reopened_comment_template: |-
            <%= sender.login %> created pull request
            
            + [<%= title %>] <%= pr.html_url %>  (#<%= pr.number %>)
          pr_ready_for_review_comment_template: |-
            <%= sender.login %> created pull request
            
            + [<%= title %>] <%= pr.html_url %>  (#<%= pr.number %>)
          pr_closed_comment_template: |-
            <%= sender.login %> closed pull request
            
            + [<%= title %>] <%= pr.html_url %>  (#<%= pr.number %>)
          pr_merged_comment_template: |-
            <%= sender.login %> merged pull request
            
            + [<%= title %>] <%= pr.html_url %>  (#<%= pr.number %>)
