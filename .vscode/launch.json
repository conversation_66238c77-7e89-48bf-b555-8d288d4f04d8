{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "dev_kc_member_site_native",
      "request": "launch",
      "type": "dart",
      "args": [
        "--debug",
        "-v",
        "--flavor",
        "develop",
        "--dart-define=FLAVOR=develop",
        "--dart-define=DOMAIN_TYPE=BFF",
        "--dart-define=GUIDE_PROXY=@guide.inc:<EMAIL>:3218"
      ],
      "program": "lib/main.dart"
    },
    {
      "name": "stg_kc_member_site_native",
      "request": "launch",
      "type": "dart",
      "args": [
        "--debug",
        "-v",
        "--flavor",
        "staging",
        "--dart-define=FLAVOR=staging",
        "--dart-define=DOMAIN_TYPE=A"
      ],
      "program": "lib/main.dart"
    },
    {
      "name": "st_kc_member_site_native",
      "request": "launch",
      "type": "dart",
      "args": [
        "--debug",
        "-v",
        "--flavor",
        "st",
        "--dart-define=FLAVOR=st",
        "--dart-define=DOMAIN_TYPE=A"
      ],
      "program": "lib/main.dart"
    },
    {
      "name": "adt_kc_member_site_native",
      "request": "launch",
      "type": "dart",
      "args": [
        "--debug",
        "-v",
        "--flavor",
        "adt",
        "--dart-define=FLAVOR=adt",
        "--dart-define=DOMAIN_TYPE=A"
      ],
      "program": "lib/main.dart"
    },
    {
      "name": "prod_kc_member_site_native",
      "request": "launch",
      "type": "dart",
      "args": [
        "--debug",
        "-v",
        "--flavor",
        "production",
        "--dart-define=FLAVOR=production"
      ],
      "program": "lib/main.dart"
    },
    {
      "name": "kc_member_site_native (profile mode)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "kc_member_site_native (release mode)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    }
  ]
}
