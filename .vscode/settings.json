{"editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.fixAll": "explicit"}, "editor.formatOnSave": true, "dart.lineLength": 140, "[dart]": {"editor.formatOnSave": true, "editor.rulers": [140], "editor.defaultFormatter": "Dart-Code.dart-code"}, "dart.flutterRunAdditionalArgs": ["--no-enable-impeller"], "dart.flutterSdkPath": ".fvm/versions/3.24.5", "search.exclude": {"**/.fvm": true}, "files.watcherExclude": {"**/.fvm": true}}