plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    // END: FlutterFire Configuration
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new FileNotFoundException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0.0'
}

android {
    signingConfigs {
        develop {
            def propsFile = rootProject.file('keystore/keystoreDev.properties')
            if (propsFile.exists()) {
                def props = new Properties()
                props.load(new FileInputStream(propsFile))
                storeFile = file(props['storeFile'])
                storePassword = props['storePassword']
                keyAlias = props['keyAlias']
                keyPassword = props['keyPassword']
            } 
        }
        staging {
            def propsFile = rootProject.file('keystore/keystoreStg.properties')
            if (propsFile.exists()) {
                def props = new Properties()
                props.load(new FileInputStream(propsFile))
                storeFile = file(props['storeFile'])
                storePassword = props['storePassword']
                keyAlias = props['keyAlias']
                keyPassword = props['keyPassword']
            } 
        }
        adt {
            def propsFile = rootProject.file('keystore/keystoreAdt.properties')
            if (propsFile.exists()) {
                def props = new Properties()
                props.load(new FileInputStream(propsFile))
                storeFile = file(props['storeFile'])
                storePassword = props['storePassword']
                keyAlias = props['keyAlias']
                keyPassword = props['keyPassword']
            } 
        }
        production {
            def propsFile = rootProject.file('keystore/keystoreProd.properties')
            if (propsFile.exists()) {
                def props = new Properties()
                props.load(new FileInputStream(propsFile))
                storeFile = file(props['storeFile'])
                storePassword = props['storePassword']
                keyAlias = props['keyAlias']
                keyPassword = props['keyPassword']
            }
        }
    }

    namespace = "com.kabu.kabuappNext"
    compileSdk = 35

    compileOptions {
        // Flag to enable support for the new language APIs
        coreLibraryDesugaringEnabled = true

        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }

    defaultConfig {
        applicationId = "com.kabu.kabuappNext"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 23 //flutter.minSdkVersion
        targetSdk = 35 //flutter.targetSdkVersion
        versionCode = flutterVersionCode.toInteger()
        versionName = flutterVersionName
        multiDexEnabled = true
    }

    buildTypes {
        release {
            // Add your own signing config for the release build => In Azure Pipelines
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.debug
        }
    }
    flavorDimensions "env"
    productFlavors {
        develop {
            dimension "env"
            applicationId "inc.guide.kabuappNext.dev"
            signingConfig signingConfigs.develop
        }
        st {
            dimension "env"
            applicationIdSuffix ".devst"
            signingConfig signingConfigs.production // No need to create own config
        }
        staging {
            dimension "env"
            applicationIdSuffix ".stg"
            signingConfig signingConfigs.staging
        }
        adt {
            dimension "env"
            applicationIdSuffix ".adt"
            signingConfig signingConfigs.adt
        }
        production {
            dimension "env"
            applicationIdSuffix ""
            signingConfig signingConfigs.production
        }
    }
    lint {
        checkReleaseBuilds false
    }
}

flutter {
    source = '../..'
}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.3'
}
