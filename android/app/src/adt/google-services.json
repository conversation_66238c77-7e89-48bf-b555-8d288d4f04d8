{"project_info": {"project_number": "297148235642", "project_id": "kabuappnext-stg", "storage_bucket": "kabuappnext-stg.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:297148235642:android:ad78c1c5102e6dbd18a0c3", "android_client_info": {"package_name": "com.kabu.kabuappNext.adt"}}, "oauth_client": [], "api_key": [{"current_key": "FIREBASE_API_KEY_ANDROID"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:297148235642:android:7529b91ad4eb421118a0c3", "android_client_info": {"package_name": "com.kabu.kabuappNext.devst"}}, "oauth_client": [], "api_key": [{"current_key": "FIREBASE_API_KEY_ANDROID"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:297148235642:android:502826e8d534a7cf18a0c3", "android_client_info": {"package_name": "com.kabu.kabuappNext.stg"}}, "oauth_client": [], "api_key": [{"current_key": "FIREBASE_API_KEY_ANDROID"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}], "configuration_version": "1"}