{"project_info": {"project_number": "297148235642", "project_id": "kabuappnext-stg", "storage_bucket": "kabuappnext-stg.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:297148235642:android:7529b91ad4eb421118a0c3", "android_client_info": {"package_name": "com.kabu.kabuappNext.devst"}}, "oauth_client": [{"client_id": "297148235642-45di4q9565ac08n69k9k68rrik3ep564.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "FIREBASE_API_KEY_ANDROID"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "297148235642-45di4q9565ac08n69k9k68rrik3ep564.apps.googleusercontent.com", "client_type": 3}, {"client_id": "297148235642-0a4bs9coj4i2gfvgp2vujfnvfu0f34qh.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.kabu.kabuappNext.devst"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:297148235642:android:502826e8d534a7cf18a0c3", "android_client_info": {"package_name": "com.kabu.kabuappNext.stg"}}, "oauth_client": [{"client_id": "297148235642-45di4q9565ac08n69k9k68rrik3ep564.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "FIREBASE_API_KEY_ANDROID"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "297148235642-45di4q9565ac08n69k9k68rrik3ep564.apps.googleusercontent.com", "client_type": 3}, {"client_id": "297148235642-0a4bs9coj4i2gfvgp2vujfnvfu0f34qh.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.kabu.kabuappNext.devst"}}]}}}], "configuration_version": "1"}