import UIKit
import Flutter
import Firebase
import Ka<PERSON><PERSON>ore

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    if #available(iOS 10.0, *) {
      UNUserNotificationCenter.current().delegate = self as? UNUserNotificationCenterDelegate
    }

    GeneratedPluginRegistrant.register(with: self)

    KarteApp.setLogLevel(.debug)
    KarteApp.setup(appKey: "kqfrrzBqkwzpeip2oiVBmvc0bfLmUity")
    
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
