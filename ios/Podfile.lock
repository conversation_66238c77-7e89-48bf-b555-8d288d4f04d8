PODS:
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - device_info_plus (0.0.1):
    - Flutter
  - Firebase/Analytics (11.4.0):
    - Firebase/Core
  - Firebase/Core (11.4.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.4.0)
  - Firebase/CoreOnly (11.4.0):
    - FirebaseCore (= 11.4.0)
  - Firebase/Crashlytics (11.4.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.4.0)
  - Firebase/Messaging (11.4.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.4.0)
  - firebase_analytics (11.3.4):
    - Firebase/Analytics (= 11.4.0)
    - firebase_core
    - Flutter
  - firebase_app_check (0.3.1-5):
    - Firebase/CoreOnly (~> 11.4.0)
    - firebase_core
    - FirebaseAppCheck (~> 11.4.0)
    - Flutter
  - firebase_core (3.7.0):
    - Firebase/CoreOnly (= 11.4.0)
    - Flutter
  - firebase_crashlytics (4.1.4):
    - Firebase/Crashlytics (= 11.4.0)
    - firebase_core
    - Flutter
  - firebase_messaging (15.1.4):
    - Firebase/Messaging (= 11.4.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (11.4.0):
    - FirebaseAnalytics/AdIdSupport (= 11.4.0)
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.4.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.4.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheck (11.4.0):
    - AppCheckCore (~> 11.0)
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
  - FirebaseAppCheckInterop (11.4.0)
  - FirebaseCore (11.4.0):
    - FirebaseCoreInternal (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.4.1):
    - FirebaseCore (~> 11.0)
  - FirebaseCoreInternal (11.4.2):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.4.0):
    - FirebaseCore (~> 11.4)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (11.4.0):
    - FirebaseCore (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.4.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfigInterop (11.4.0)
  - FirebaseSessions (11.4.0):
    - FirebaseCore (~> 11.4)
    - FirebaseCoreExtension (~> 11.4)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - Flutter (1.0.0)
  - flutter_apns_only (0.0.1):
    - Flutter
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_jailbreak_detection (1.0.0):
    - Flutter
    - IOSSecuritySuite
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_pdfview (1.0.2):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - flutter_web_browser (0.17.1):
    - Flutter
  - GoogleAppMeasurement (11.4.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.4.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.4.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.4.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.4.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - in_app_review (0.2.0):
    - Flutter
  - IOSSecuritySuite (2.1.0)
  - karte_core (1.3.0):
    - Flutter
    - KarteCore (~> 2.21)
  - karte_notification (1.4.0):
    - Flutter
    - KarteRemoteNotification (~> 2)
  - KarteCore (2.29.0):
    - KarteUtilities (~> 3.8)
  - KarteRemoteNotification (2.11.0):
    - KarteCore (~> 2.0)
    - KarteUtilities (~> 3.8)
  - KarteUtilities (3.12.0):
    - KarteUtilities/standard (= 3.12.0)
  - KarteUtilities/standard (3.12.0)
  - move_to_background (0.0.1):
    - Flutter
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - native_flutter_proxy (0.0.1):
    - Flutter
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - uni_links (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_cookie_manager (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_app_check (from `.symlinks/plugins/firebase_app_check/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_apns_only (from `.symlinks/plugins/flutter_apns_only/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_jailbreak_detection (from `.symlinks/plugins/flutter_jailbreak_detection/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_pdfview (from `.symlinks/plugins/flutter_pdfview/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - flutter_web_browser (from `.symlinks/plugins/flutter_web_browser/ios`)
  - in_app_review (from `.symlinks/plugins/in_app_review/ios`)
  - karte_core (from `.symlinks/plugins/karte_core/ios`)
  - karte_notification (from `.symlinks/plugins/karte_notification/ios`)
  - move_to_background (from `.symlinks/plugins/move_to_background/ios`)
  - native_flutter_proxy (from `.symlinks/plugins/native_flutter_proxy/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - uni_links (from `.symlinks/plugins/uni_links/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_cookie_manager (from `.symlinks/plugins/webview_cookie_manager/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - AppCheckCore
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheck
    - FirebaseAppCheckInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - IOSSecuritySuite
    - KarteCore
    - KarteRemoteNotification
    - KarteUtilities
    - nanopb
    - OrderedSet
    - PromisesObjC
    - PromisesSwift

EXTERNAL SOURCES:
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_app_check:
    :path: ".symlinks/plugins/firebase_app_check/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_apns_only:
    :path: ".symlinks/plugins/flutter_apns_only/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_jailbreak_detection:
    :path: ".symlinks/plugins/flutter_jailbreak_detection/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_pdfview:
    :path: ".symlinks/plugins/flutter_pdfview/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  flutter_web_browser:
    :path: ".symlinks/plugins/flutter_web_browser/ios"
  in_app_review:
    :path: ".symlinks/plugins/in_app_review/ios"
  karte_core:
    :path: ".symlinks/plugins/karte_core/ios"
  karte_notification:
    :path: ".symlinks/plugins/karte_notification/ios"
  move_to_background:
    :path: ".symlinks/plugins/move_to_background/ios"
  native_flutter_proxy:
    :path: ".symlinks/plugins/native_flutter_proxy/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  uni_links:
    :path: ".symlinks/plugins/uni_links/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_cookie_manager:
    :path: ".symlinks/plugins/webview_cookie_manager/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  Firebase: cf1b19f21410b029b6786a54e9764a0cacad3c99
  firebase_analytics: dc8228ac09a0f410729eb2ec6524a3bb46d05576
  firebase_app_check: 99f0580ffde066a7fe351ba1dc0bd9b1656c778d
  firebase_core: 059a3a579c00e241fcdcd66d17190878f4f6ed53
  firebase_crashlytics: a29fae082bf9106c75dc992d4c268287e003a7fb
  firebase_messaging: 2cf2397ef8ae51d39da705250e89c5cfeff57527
  FirebaseAnalytics: 3feef9ae8733c567866342a1000691baaa7cad49
  FirebaseAppCheck: 933cbda29279ed316b82360bca77602ac1af1ff2
  FirebaseAppCheckInterop: 1b9643ae2f1ee214488caa2f8e32b7bc2f0f3735
  FirebaseCore: e0510f1523bc0eb21653cac00792e1e2bd6f1771
  FirebaseCoreExtension: f1bc67a4702931a7caa097d8e4ac0a1b0d16720e
  FirebaseCoreInternal: 35731192cab10797b88411be84940d2beb33a238
  FirebaseCrashlytics: 41bbdd2b514a8523cede0c217aee6ef7ecf38401
  FirebaseInstallations: 6ef4a1c7eb2a61ee1f74727d7f6ce2e72acf1414
  FirebaseMessaging: f8a160d99c2c2e5babbbcc90c4a3e15db036aee2
  FirebaseRemoteConfigInterop: e76f46ffa4d6a65e273d4dfebb6a79e588cec136
  FirebaseSessions: 3f56f177d9e53a85021d16b31f9a111849d1dd8b
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_apns_only: bd278c53433cb01cd66152843f7247fe10b9a9ea
  flutter_inappwebview_ios: b89ba3482b96fb25e00c967aae065701b66e9b99
  flutter_jailbreak_detection: a74360daaf5cbc80639959463b4b1395873c90b5
  flutter_local_notifications: 395056b3175ba4f08480a7c5de30cd36d69827e4
  flutter_pdfview: 54e283d5851b0b247b3cc57877d35f1a05a204de
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  flutter_web_browser: 8fe4d18e7b1328ab3fbec6e67029d6996c2335d9
  GoogleAppMeasurement: 987769c4ca6b968f2479fbcc9fe3ce34af454b8e
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  in_app_review: 8efcf4a4d3ba72d5d776d29e5a268f1abf64d184
  IOSSecuritySuite: 45e8531b05ffa72b5661cbdb5b5b5648a8de1a84
  karte_core: 84788620e41967905e1f0ca21fc5f2141fa9edc7
  karte_notification: 5e63c943752f3cffedd8e65531fe0aca1253b50e
  KarteCore: 2a9e5aa0628d484bbd7121958676ac6d32775e36
  KarteRemoteNotification: c3618f473e56d21f48d3b85ac31414452e2cb953
  KarteUtilities: 2683d3589dd9fa860d87bec69447d188174f21b3
  move_to_background: 155f7bfbd34d43ad847cb630d2d2d87c17199710
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  native_flutter_proxy: a96434620247e3a0e3ebffde6dae1c31e2745256
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  uni_links: ed8c961e47ed9ce42b6d91e1de8049e38a4b3152
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  webview_cookie_manager: d63a76cabdf42a7ea3d92768ac67d4853a1367f8
  webview_flutter_wkwebview: 44d4dee7d7056d5ad185d25b38404436d56c547c

PODFILE CHECKSUM: 536604cecf63474bec74cf79e7565c5e9a511e66

COCOAPODS: 1.16.2
