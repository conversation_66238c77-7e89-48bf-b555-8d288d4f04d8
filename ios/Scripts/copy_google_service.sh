#!/bin/sh
FIREBASE_INFO_PLIST="GoogleService-Info.plist"
# This is the default location where Firebase init code expects to find GoogleServices-Info.plist file.
PLIST_DESTINATION=${BUILT_PRODUCTS_DIR}/${PRODUCT_NAME}.app/${FIREBASE_INFO_PLIST}

if [ "${CONFIGURATION}" == "Debug-production" ] || [ "${CONFIGURATION}" == "Release-production" ] || [ "${CONFIGURATION}" == "Profile-production" ]; then
cp "${PROJECT_DIR}/config/prod/${FIREBASE_INFO_PLIST}" "${PLIST_DESTINATION}"
echo "Production plist copied"
elif [ "${CONFIGURATION}" == "Debug-develop" ] || [ "${CONFIGURATION}" == "Release-develop" ] || [ "${CONFIGURATION}" == "Release-developStore" ] || [ "${CONFIGURATION}" == "Profile-develop" ]; then
cp "${PROJECT_DIR}/config/dev/${FIREBASE_INFO_PLIST}" "${PLIST_DESTINATION}"
echo "Develop plist copied"
elif [ "${CONFIGURATION}" == "Debug-staging" ] || [ "${CONFIGURATION}" == "Release-staging" ] || [ "${CONFIGURATION}" == "Release-stagingStore" ] || [ "${CONFIGURATION}" == "Profile-staging" ]; then
cp "${PROJECT_DIR}/config/stg/${FIREBASE_INFO_PLIST}" "${PLIST_DESTINATION}"
echo "Staging plist copied"
elif [ "${CONFIGURATION}" == "Debug-st" ] || [ "${CONFIGURATION}" == "Release-st" ] || [ "${CONFIGURATION}" == "Profile-st" ]; then
cp "${PROJECT_DIR}/config/st/${FIREBASE_INFO_PLIST}" "${PLIST_DESTINATION}"
echo "St plist copied"
elif [ "${CONFIGURATION}" == "Debug-adt" ] || [ "${CONFIGURATION}" == "Release-adt" ] || [ "${CONFIGURATION}" == "Profile-adt" ]; then
cp "${PROJECT_DIR}/config/adt/${FIREBASE_INFO_PLIST}" "${PLIST_DESTINATION}"
echo "Adt plist copied"
fi
