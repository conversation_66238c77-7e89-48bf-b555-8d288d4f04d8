#!/bin/sh
FIREBASE_APP_ID_FILE="firebase_app_id_file.json"

if [ "${CONFIGURATION}" == "Debug-production" ] || [ "${CONFIGURATION}" == "Release-production" ] || [ "${CONFIGURATION}" == "Profile-production" ]; then
"$PODS_ROOT/FirebaseCrashlytics/upload-symbols" --flutter-project "$PROJECT_DIR/config/prod/${FIREBASE_APP_ID_FILE}"
echo "Production FirebaseCrashlytics upload-symbols done"
elif [ "${CONFIGURATION}" == "Debug-develop" ] || [ "${CONFIGURATION}" == "Release-develop" ] || [ "${CONFIGURATION}" == "Release-developStore" ] || [ "${CONFIGURATION}" == "Profile-develop" ]; then
"$PODS_ROOT/FirebaseCrashlytics/upload-symbols" --flutter-project "$PROJECT_DIR/config/dev/${FIREBASE_APP_ID_FILE}"
echo "Develop FirebaseCrashlytics upload-symbols done"
elif [ "${CONFIGURATION}" == "Debug-staging" ] || [ "${CONFIGURATION}" == "Release-staging" ] || [ "${CONFIGURATION}" == "Release-stagingStore" ] || [ "${CONFIGURATION}" == "Profile-staging" ]; then
"$PODS_ROOT/FirebaseCrashlytics/upload-symbols" --flutter-project "$PROJECT_DIR/config/stg/${FIREBASE_APP_ID_FILE}"
echo "Staging FirebaseCrashlytics upload-symbols done"
elif [ "${CONFIGURATION}" == "Debug-st" ] || [ "${CONFIGURATION}" == "Release-st" ] || [ "${CONFIGURATION}" == "Profile-st" ]; then
"$PODS_ROOT/FirebaseCrashlytics/upload-symbols" --flutter-project "$PROJECT_DIR/config/st/${FIREBASE_APP_ID_FILE}"
echo "St FirebaseCrashlytics upload-symbols done"
elif [ "${CONFIGURATION}" == "Debug-adt" ] || [ "${CONFIGURATION}" == "Release-adt" ] || [ "${CONFIGURATION}" == "Profile-adt" ]; then
"$PODS_ROOT/FirebaseCrashlytics/upload-symbols" --flutter-project "$PROJECT_DIR/config/adt/${FIREBASE_APP_ID_FILE}"
echo "Adt FirebaseCrashlytics upload-symbols done"
fi
